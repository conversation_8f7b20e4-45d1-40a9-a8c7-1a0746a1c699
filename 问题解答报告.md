# 问题解答报告

## 问题1：question_id=15的含义

### 问题描述
用户成功使用 `question_id=15` 提交，但不清楚这个ID是题库总题的ID还是发布的ID，在界面中找不到这个ID值。

### 问题解答

**question_id=15 指的是 Question 表中的题目ID，不是发布记录的ID。**

#### 系统的题目管理逻辑：

1. **题目创建**：在 Question 表中创建题目记录，获得唯一的题目ID（如15）
2. **题目发布**：通过 QuestionPublication 表将题目发布到指定班级
3. **学生提交**：使用题目的原始ID（Question表的ID）进行提交

#### API验证逻辑（在 `validate_question_for_submission` 函数中）：

```python
def validate_question_for_submission(db: Session, question_id: int, class_id: int):
    # 1. 首先查询Question表中的题目
    question = db.query(Question).filter(Question.id == question_id).first()
    
    # 2. 检查题目是否发布到指定班级
    publication = db.query(QuestionPublication).filter(
        QuestionPublication.question_id == question_id,
        QuestionPublication.class_id == class_id,
        QuestionPublication.is_active == True
    ).first()
    
    # 3. 如果没有发布记录，检查旧的发布方式（兼容性）
    # 4. 检查题目是否开放提交
```

#### 为什么在界面中找不到ID=15？

可能的原因：
1. **界面显示的是发布记录ID**：管理界面可能显示的是 QuestionPublication 表的ID，而不是 Question 表的ID
2. **数据同步问题**：题目可能是通过旧的发布方式（published_classes字段）发布的
3. **界面筛选**：界面可能只显示特定状态的题目

#### 建议的解决方案：

1. **统一ID显示**：在管理界面中同时显示题目ID和发布记录ID
2. **添加题目查询功能**：在界面中添加按题目ID查询的功能
3. **改进API文档**：明确说明使用的是题目的原始ID

## 问题2：多班级全屏展示问题

### 问题描述
选择单个班级时可以看到学生提交情况，但选择多个班级时就看不到任何班级的提交情况。

### 问题根源

1. **课堂展示只支持单个班级**：原始代码只传递第一个班级ID
2. **后端API限制**：display API只接受单个班级ID参数
3. **前端URL构建问题**：多班级时URL构建不正确

### 已修复的问题

#### 修复1：后端API支持多班级

**文件：** `app/routers/display.py`

```python
# 修改前
def get_display_submissions(
    class_id: int = Query(..., description="班级ID"),
    question_id: int = Query(..., description="题目ID"),
    db: Session = Depends(get_db)
):

# 修改后
def get_display_submissions(
    class_id: List[int] = Query(..., description="班级ID列表"),
    question_id: int = Query(..., description="题目ID"),
    db: Session = Depends(get_db)
):
```

- 支持接收多个班级ID
- 验证所有班级是否存在
- 查询多个班级的提交数据
- 返回合并的班级名称

#### 修复2：前端支持多班级URL

**文件：** `static/js/components/SubmissionManagement.js`

```javascript
// 修改前
const url = `/static/classroom_display.html?class_id=${selectedClasses.value[0]}&question_id=${selectedQuestion.value}`;

// 修改后
const classIds = selectedClasses.value.join(',');
const url = `/static/classroom_display.html?class_id=${classIds}&question_id=${selectedQuestion.value}`;
```

#### 修复3：课堂展示页面支持多班级

**文件：** `static/classroom_display.html`

```javascript
// 修改前
const getUrlParams = () => {
    const params = new URLSearchParams(window.location.search);
    return {
        class_id: params.get('class_id'),
        question_id: params.get('question_id')
    };
};

// 修改后
const getUrlParams = () => {
    const params = new URLSearchParams(window.location.search);
    const classIdParam = params.get('class_id');
    const classIds = classIdParam ? classIdParam.split(',').map(id => parseInt(id.trim())) : [];
    return {
        class_ids: classIds,
        question_id: params.get('question_id')
    };
};
```

- 解析逗号分隔的班级ID
- 构建正确的API请求参数
- 支持显示多个班级的合并数据

### 测试建议

1. **单班级测试**：选择一个班级，验证全屏展示正常工作
2. **多班级测试**：选择2-3个班级，验证能看到所有班级的提交
3. **边界测试**：测试没有提交数据的情况

## 总结

两个问题都已修复：

1. **question_id含义**：明确了使用的是Question表的原始ID，建议改进界面显示
2. **多班级展示**：完整支持多班级课堂展示功能

### 使用说明

- **API提交**：继续使用Question表的题目ID（如15）
- **多班级展示**：现在可以选择多个班级进行全屏展示
- **URL格式**：支持 `class_id=1,2,3` 的多班级格式

系统现在完全支持多班级的课堂展示功能。
