<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试课堂展示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-link {
            display: block;
            padding: 15px;
            margin: 10px 0;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #0056b3;
        }
        .description {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 课堂展示功能测试</h1>
        
        <h2>📋 功能说明</h2>
        <div class="description">
            <p><strong>新功能：</strong></p>
            <ul>
                <li>✅ 从主导航栏移除了性能排行榜</li>
                <li>✅ 在全屏显示中添加了视图切换功能</li>
                <li>✅ 支持提交展示和性能排行榜两种视图</li>
                <li>✅ 排行榜支持可控制的显示选项（分数条、运行时间、硬件信息）</li>
                <li>✅ 右上角控制面板可以切换视图和显示选项</li>
            </ul>
        </div>

        <h2>🔗 测试链接</h2>
        
        <a href="/static/classroom_display.html?class_id=1&question_id=1" 
           class="test-link" target="_blank">
            📊 测试课堂展示 - 班级1，题目1
        </a>
        <div class="description">
            <strong>测试内容：</strong>
            <ul>
                <li>默认显示提交展示视图</li>
                <li>右上角有控制面板</li>
                <li>可以切换到性能排行榜视图</li>
                <li>排行榜显示性能分数、运行时间、硬件信息</li>
            </ul>
        </div>

        <a href="/static/classroom_display.html?class_id=1,2&question_id=1" 
           class="test-link" target="_blank">
            📊 测试课堂展示 - 多班级，题目1
        </a>
        <div class="description">
            <strong>测试内容：</strong>
            <ul>
                <li>支持多个班级的提交展示</li>
                <li>排行榜会显示所有班级的学生</li>
            </ul>
        </div>

        <h2>📝 测试步骤</h2>
        <div class="description">
            <ol>
                <li><strong>点击测试链接</strong> - 打开全屏显示页面</li>
                <li><strong>查看提交展示</strong> - 默认显示学生提交的作业</li>
                <li><strong>切换到排行榜</strong> - 点击右上角控制面板中的"性能排行榜"</li>
                <li><strong>测试显示选项</strong> - 勾选/取消勾选分数条、运行时间、硬件信息</li>
                <li><strong>观察排行榜</strong> - 查看性能分数条、排名徽章等</li>
            </ol>
        </div>

        <h2>🎯 预期结果</h2>
        <div class="description">
            <ul>
                <li>✅ 右上角显示控制面板</li>
                <li>✅ 可以在提交展示和排行榜之间切换</li>
                <li>✅ 排行榜显示前三名特殊徽章（王冠、奖牌、奖杯）</li>
                <li>✅ 性能分数以横条图显示</li>
                <li>✅ 可以控制显示哪些信息</li>
                <li>✅ 页面标题根据视图模式变化</li>
            </ul>
        </div>

        <h2>📊 测试数据</h2>
        <div class="description">
            <p>当前系统中有以下测试数据：</p>
            <ul>
                <li><strong>杨智慧</strong> - 运行时间: 1.8s, 性能分数: 555.6, 硬件: AMD Ryzen 9 5900X + RTX 3070</li>
                <li><strong>学生_2022003</strong> - 运行时间: 2.5s, 性能分数: 400.0, 硬件: Intel i7-12700K + RTX 3080</li>
            </ul>
        </div>
    </div>
</body>
</html>
