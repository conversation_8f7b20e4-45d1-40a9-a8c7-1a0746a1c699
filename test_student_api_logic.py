#!/usr/bin/env python3
"""
测试学生端API逻辑（不需要启动服务器）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models import Question, QuestionPublication, Class
from sqlalchemy.orm import joinedload

def test_student_questions_logic():
    """测试学生端获取题目列表的逻辑"""
    print("=== 测试学生端获取题目列表逻辑 ===")
    
    db = SessionLocal()
    try:
        # 获取激活班级
        active_class = db.query(Class).filter(Class.is_active == True).first()
        if not active_class:
            print("❌ 没有激活的班级")
            return []
        
        print(f"✅ 找到激活班级: {active_class.name} (ID: {active_class.id})")
        
        # 获取发布到该班级的激活题目
        publications = db.query(QuestionPublication).options(
            joinedload(QuestionPublication.question)
        ).filter(
            QuestionPublication.class_id == active_class.id,
            QuestionPublication.is_active == True
        ).all()
        
        print(f"✅ 找到 {len(publications)} 个发布实例")
        
        # 构建返回数据
        result = []
        for pub in publications:
            question = pub.question
            # 只返回开放提交的题目
            if question and question.is_open:
                question_data = {
                    "id": pub.id,  # 使用发布实例ID
                    "publication_id": pub.id,  # 明确标识这是发布实例ID
                    "question_id": question.id,  # 原始题目ID（用于兼容性）
                    "title": question.title,
                    "description": question.description,
                    "submission_type": question.submission_type,
                    "question_type": getattr(question, 'question_type', 'submit_result'),
                    "published_at": pub.published_at,
                    "class_name": active_class.name
                }
                result.append(question_data)
                print(f"  - 发布ID: {pub.id}, 题目ID: {question.id}, 标题: {question.title}")
            else:
                print(f"  - 跳过发布ID: {pub.id} (题目不存在或未开放)")
        
        # 按发布时间排序
        result.sort(key=lambda x: x["published_at"], reverse=True)
        
        return result
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
        return []
    finally:
        db.close()

def test_publication_id_validation():
    """测试publication_id验证逻辑"""
    print("\n=== 测试publication_id验证逻辑 ===")
    
    db = SessionLocal()
    try:
        # 获取激活班级
        active_class = db.query(Class).filter(Class.is_active == True).first()
        if not active_class:
            print("❌ 没有激活的班级")
            return False
        
        # 获取一个有效的publication_id
        publication = db.query(QuestionPublication).filter(
            QuestionPublication.class_id == active_class.id,
            QuestionPublication.is_active == True
        ).first()
        
        if not publication:
            print("❌ 没有找到有效的发布实例")
            return False
        
        print(f"✅ 测试publication_id: {publication.id}")
        
        # 验证逻辑
        test_publication = db.query(QuestionPublication).filter(
            QuestionPublication.id == publication.id,
            QuestionPublication.class_id == active_class.id,
            QuestionPublication.is_active == True
        ).first()
        
        if test_publication:
            question = test_publication.question
            if question and question.is_open:
                print(f"✅ 验证成功: 发布ID {publication.id} -> 题目ID {question.id} ({question.title})")
                return True
            else:
                print(f"❌ 题目不存在或未开放")
                return False
        else:
            print(f"❌ 发布实例不存在或未激活")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    finally:
        db.close()

def test_backward_compatibility():
    """测试向后兼容性（question_id）"""
    print("\n=== 测试向后兼容性（question_id） ===")
    
    db = SessionLocal()
    try:
        # 获取激活班级
        active_class = db.query(Class).filter(Class.is_active == True).first()
        if not active_class:
            print("❌ 没有激活的班级")
            return False
        
        # 获取一个开放的题目
        question = db.query(Question).filter(Question.is_open == True).first()
        if not question:
            print("❌ 没有找到开放的题目")
            return False
        
        print(f"✅ 测试question_id: {question.id} ({question.title})")
        
        # 检查题目是否发布到指定班级
        publication = db.query(QuestionPublication).filter(
            QuestionPublication.question_id == question.id,
            QuestionPublication.class_id == active_class.id,
            QuestionPublication.is_active == True
        ).first()
        
        if publication:
            print(f"✅ 向后兼容验证成功: 题目ID {question.id} 已发布到班级 {active_class.id}")
            return True
        else:
            # 检查旧的发布方式（兼容性）
            if question.is_published and question.published_classes:
                try:
                    import json
                    published_class_ids = json.loads(question.published_classes)
                    if active_class.id in published_class_ids:
                        print(f"✅ 向后兼容验证成功: 题目ID {question.id} 通过旧方式发布到班级 {active_class.id}")
                        return True
                except:
                    pass
            
            print(f"❌ 题目ID {question.id} 未发布到班级 {active_class.id}")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False
    finally:
        db.close()

def main():
    """主测试函数"""
    print("🧪 开始测试学生端API逻辑")
    print("=" * 50)
    
    # 1. 测试获取题目列表逻辑
    questions = test_student_questions_logic()
    
    # 2. 测试publication_id验证逻辑
    pub_valid = test_publication_id_validation()
    
    # 3. 测试向后兼容性
    backward_valid = test_backward_compatibility()
    
    print("\n" + "=" * 50)
    print("🏁 测试总结:")
    print(f"  获取题目列表: {'✅ 成功' if questions else '❌ 失败'} ({len(questions)} 个题目)")
    print(f"  publication_id验证: {'✅ 成功' if pub_valid else '❌ 失败'}")
    print(f"  向后兼容性: {'✅ 成功' if backward_valid else '❌ 失败'}")
    
    if questions and pub_valid and backward_valid:
        print("\n🎉 所有逻辑测试通过！")
        print("\n📋 可用题目列表:")
        for q in questions:
            print(f"  - 发布ID: {q['id']}, 题目ID: {q['question_id']}, 标题: {q['title']}")
    else:
        print("\n⚠️  部分逻辑测试失败。")

if __name__ == "__main__":
    main()
