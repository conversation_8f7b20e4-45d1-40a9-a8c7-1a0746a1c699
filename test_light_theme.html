<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浅色主题性能排行榜测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: 
                radial-gradient(circle at 20% 80%, rgba(52, 152, 219, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(46, 204, 113, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(155, 89, 182, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
            color: #2c3e50;
            overflow-x: hidden;
            min-height: 100vh;
            padding: 2rem;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 
                0 8px 32px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.8);
            border: 1px solid rgba(52, 152, 219, 0.2);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 控制面板 */
        .control-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            color: #2c3e50;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 
                0 8px 32px rgba(0,0,0,0.15),
                inset 0 1px 0 rgba(255,255,255,0.8);
            border: 1px solid rgba(52, 152, 219, 0.2);
            z-index: 1000;
            min-width: 180px;
        }

        .control-panel h3 {
            margin-bottom: 1rem;
            font-size: 1rem;
            color: #2c3e50;
            font-weight: 600;
        }

        /* 排行榜项目 */
        .leaderboard-item {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            color: #2c3e50;
            border-radius: 12px;
            padding: 1rem 1.2rem;
            margin-bottom: 0.8rem;
            box-shadow: 
                0 4px 16px rgba(0,0,0,0.08),
                inset 0 1px 0 rgba(255,255,255,0.8);
            border: 1px solid rgba(52, 152, 219, 0.15);
            display: flex;
            align-items: center;
            gap: 1.5rem;
            min-height: 70px;
            transition: all 0.3s ease;
        }

        .leaderboard-item:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 12px 40px rgba(52, 152, 219, 0.2),
                inset 0 1px 0 rgba(255,255,255,0.9);
            border-color: rgba(52, 152, 219, 0.3);
        }

        .rank-badge {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1rem;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .rank-1 { background: linear-gradient(135deg, #ffd700, #ffed4e); color: #b8860b; }
        .rank-2 { background: linear-gradient(135deg, #c0c0c0, #e8e8e8); color: #666; }
        .rank-3 { background: linear-gradient(135deg, #cd7f32, #daa520); color: #8b4513; }
        .rank-other { background: linear-gradient(135deg, #6c757d, #495057); color: #fff; }

        /* 学生信息区域 - 1/3宽度 */
        .student-info {
            flex: 1;
            min-width: 200px;
            max-width: 300px;
            margin-right: 1rem;
            padding: 0.5rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid #3498db;
        }

        .student-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.3rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .student-name::before {
            content: "👤";
            font-size: 1rem;
        }

        .hardware-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #34495e;
            background: rgba(52, 152, 219, 0.1);
            padding: 0.3rem 0.6rem;
            border-radius: 6px;
            border: 1px solid rgba(52, 152, 219, 0.2);
            font-weight: 500;
        }

        .hardware-info i {
            color: #3498db;
            font-size: 1rem;
            flex-shrink: 0;
        }

        .hardware-label {
            font-size: 0.75rem;
            color: #7f8c8d;
            margin-right: 0.3rem;
        }

        /* 性能区域 - 2/3宽度 */
        .performance-section {
            flex: 2;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .score-bar-wrapper {
            flex: 1;
            position: relative;
            min-width: 200px;
        }

        .score-bar-track {
            height: 24px;
            background: linear-gradient(90deg, #e9ecef, #dee2e6);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid rgba(52, 152, 219, 0.2);
        }

        .score-bar-fill {
            height: 100%;
            border-radius: 12px;
            position: relative;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 2px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .score-bar-shine {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255,255,255,0.4) 0%, transparent 100%);
            border-radius: 12px 12px 0 0;
        }

        .score-text {
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            color: #ffffff;
            font-size: 0.85rem;
            font-weight: 700;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
            font-family: 'Courier New', monospace;
        }

        .runtime-info {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            color: #6c757d;
            font-size: 0.8rem;
            font-weight: 500;
            min-width: 70px;
            max-width: 70px;
            justify-content: flex-end;
            flex-shrink: 0;
            background: rgba(108, 117, 125, 0.1);
            padding: 0.3rem 0.5rem;
            border-radius: 6px;
        }

        .runtime-info i {
            color: #e67e22;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 控制面板 -->
        <div class="control-panel">
            <h3><i class="fas fa-cog"></i> 显示控制</h3>
            <div class="control-option">
                <input type="checkbox" id="show-score" checked>
                <label for="show-score">显示分数条</label>
            </div>
            <div class="control-option">
                <input type="checkbox" id="show-runtime" checked>
                <label for="show-runtime">显示运行时间</label>
            </div>
            <div class="control-option">
                <input type="checkbox" id="show-hardware" checked>
                <label for="show-hardware">显示硬件信息</label>
            </div>
        </div>

        <div class="test-container">
            <div class="header">
                <h1>
                    <i class="fas fa-trophy"></i>
                    浅色主题性能排行榜
                </h1>
                <p>学生信息1/3宽度 - 进度条2/3宽度 - 硬件信息清晰显示</p>
            </div>

            <div class="leaderboard-container">
                <div v-for="(item, index) in testData" :key="index" class="leaderboard-item">
                    <div class="rank-badge" :class="{
                        'rank-1': index === 0,
                        'rank-2': index === 1,
                        'rank-3': index === 2,
                        'rank-other': index > 2
                    }">
                        <i v-if="index === 0" class="fas fa-crown"></i>
                        <i v-else-if="index === 1" class="fas fa-medal"></i>
                        <i v-else-if="index === 2" class="fas fa-award"></i>
                        <span v-else>{{ index + 1 }}</span>
                    </div>

                    <div class="student-info">
                        <div class="student-name">{{ item.student_no }}</div>
                        <div class="hardware-info">
                            <i class="fas fa-microchip"></i>
                            <span class="hardware-label">GPU:</span>
                            <strong>{{ item.hardware }}</strong>
                        </div>
                    </div>
                    
                    <div class="performance-section">
                        <div class="score-bar-wrapper">
                            <div class="score-bar-track">
                                <div class="score-bar-fill" 
                                     :style="{ 
                                         width: getScoreBarWidth(item.score) + '%',
                                         background: getScoreGradient(item.score)
                                     }">
                                    <div class="score-bar-shine"></div>
                                </div>
                                <div class="score-text">{{ item.score.toFixed(0) }}分</div>
                            </div>
                        </div>
                        
                        <div class="runtime-info">
                            <i class="fas fa-clock"></i>
                            {{ item.runtime.toFixed(3) }}s
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const testData = ref([
                    { student_no: 'CS2021001', score: 1250, runtime: 0.123, hardware: 'RTX 4090' },
                    { student_no: 'CS2021002', score: 1180, runtime: 0.145, hardware: 'RTX 4080 Super' },
                    { student_no: 'CS2021003', score: 1050, runtime: 0.167, hardware: 'RTX 4070 Ti' },
                    { student_no: 'CS2021004', score: 890, runtime: 0.189, hardware: 'RTX 3080 Ti' },
                    { student_no: 'CS2021005', score: 750, runtime: 0.234, hardware: 'RTX 3070' },
                    { student_no: 'CS2021006', score: 680, runtime: 0.267, hardware: 'GTX 1660 Super' },
                    { student_no: 'CS2021007', score: 520, runtime: 0.345, hardware: 'GTX 1650' },
                    { student_no: 'CS2021008', score: 450, runtime: 0.456, hardware: 'GTX 1050 Ti' },
                    { student_no: 'CS2021009', score: 380, runtime: 0.567, hardware: 'Intel UHD 630' },
                    { student_no: 'CS2021010', score: 320, runtime: 0.678, hardware: 'Intel HD 4000' }
                ]);

                const getScoreBarWidth = (score) => {
                    const maxScore = Math.max(...testData.value.map(item => item.score));
                    if (maxScore === 0) return 0;
                    const width = Math.min((score / maxScore) * 100, 100);
                    return Math.max(width, 1);
                };

                const getScoreGradient = (score) => {
                    if (score >= 1000) {
                        return 'linear-gradient(90deg, #e74c3c 0%, #c0392b 100%)'; // 传奇红色
                    } else if (score >= 500) {
                        return 'linear-gradient(90deg, #3498db 0%, #2980b9 100%)'; // 优秀蓝色
                    } else if (score >= 300) {
                        return 'linear-gradient(90deg, #2ecc71 0%, #27ae60 100%)'; // 良好绿色
                    } else if (score >= 100) {
                        return 'linear-gradient(90deg, #f39c12 0%, #e67e22 100%)'; // 一般橙色
                    } else {
                        return 'linear-gradient(90deg, #95a5a6 0%, #7f8c8d 100%)'; // 较差灰色
                    }
                };

                return {
                    testData,
                    getScoreBarWidth,
                    getScoreGradient
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
