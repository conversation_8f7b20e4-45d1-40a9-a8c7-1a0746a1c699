import math
import matplotlib.pyplot as plt
import requests
import json
import time
from io import BytesIO
import numpy as np # 导入 numpy 用于 np.arange
from submit_helper import submit_answer
# 记录开始时间
start_time = time.time()

# 定义全局常量
v0 = 700
k = 4.0e-5
dt = 0.1
g = 9.8

def calculate_range(angle):
    """
    计算给定发射角度下的射程。
    考虑了空气阻力。
    """
    x = 0.0
    y = 0.0
    
    # 将角度转换为弧度
    angle_rad = angle * math.pi / 180
    
    # 计算初始速度分量
    vx = v0 * math.cos(angle_rad)
    vy = v0 * math.sin(angle_rad)
    
    # 模拟弹道轨迹，直到物体落地（y < 0）
    while y >= 0:
        prev_y = y
        prev_x = x
        
        # 计算当前速度的模
        v = math.sqrt(vx * vx + vy * vy)
        
        # 更新位置
        x = x + vx * dt
        y = y + vy * dt
        
        # 更新速度分量，考虑空气阻力和重力
        vx = vx - k * v * vx * dt
        vy = vy - g * dt - k * v * vy * dt
    
    # 进行线性插值，精确计算落地点的x坐标
    # 当 y 首次变为负数时，表示物体已经穿过地面
    # prev_y 是落地前最后一个正的y坐标
    # y 是落地后第一个负的y坐标
    r = -prev_y / (y - prev_y)
    range_val = prev_x + r * (x - prev_x)
    
    return range_val

# 主程序
if __name__ == "__main__":
    angle_data = []
    range_data = []

    # 循环计算不同角度下的射程
    for angle in range(0, 91, 1): # 从0到90，步长为1
        current_range = calculate_range(angle)
        angle_data.append(angle)
        range_data.append(current_range)

    # 绘制结果
    plt.figure(figsize=(10, 6))
    plt.plot(angle_data, range_data, marker='o', linestyle='-', markersize=4)
    plt.title('Projectile Range vs. Launch Angle (with Air Resistance)')
    plt.xlabel('Launch Angle (degrees)')
    plt.ylabel('Range (m)')
    plt.grid(True)
    plt.xticks(np.arange(0, 91, 5)) # 设置x轴刻度，每5度一个
    plt.yticks(np.arange(0, max(range_data) * 1.1, 1000)) # 设置y轴刻度
    plt.tight_layout()

    # 找到最大射程及其对应的角度
    max_range = 0
    optimal_angle = 0
    for i in range(len(range_data)):
        if range_data[i] > max_range:
            max_range = range_data[i]
            optimal_angle = angle_data[i]
    
    plt.plot(optimal_angle, max_range, 'ro', markersize=8, label=f'Max Range: {max_range:.2f}m at {optimal_angle}°')
    plt.legend()

    
    # 测试使用publication_id提交
    print("=== 测试使用publication_id提交 ===")
    try:
        result1 = submit_answer(
            student_no='2023171009',
            publication_id=1,  # 使用发布实例ID
            fig=plt.gcf(), # 获取当前图形对象
            note=f'弹道计算结果，最大射程：{max_range:.2f}m @ {optimal_angle}°',
            runtime=time.time() - start_time,  # 计算运行时间
            hardware="腾讯云服务器"  # 硬件信息示例
        )
        print(f"提交结果: {result1}")
    except Exception as e:
        print(f"提交失败: {e}")

    # 测试不同的publication_id
    print("\n=== 测试不同的publication_id ===")
    try:
        result2 = submit_answer(
            student_no='2023171010',
            publication_id=2,  # 使用不同的发布实例ID
            fig=plt.gcf(),
            note=f'测试提交，最大射程：{max_range:.2f}m @ {optimal_angle}°',
            runtime=time.time() - start_time,
            hardware="本地测试机"
        )
        print(f"提交结果: {result2}")
    except Exception as e:
        print(f"提交失败: {e}")

    print("测试完成")
    # time.sleep(10)
    # plt.show()


