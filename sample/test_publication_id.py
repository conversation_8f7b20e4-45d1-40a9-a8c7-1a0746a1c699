#!/usr/bin/env python3
"""
测试publication_id的使用
"""
import requests
import json

SERVER_URL = "http://43.155.146.157:30200"

def test_publication_id():
    """测试使用publication_id提交"""
    print("=== 测试publication_id提交 ===")
    
    # 准备测试数据
    data = {
        'student_no': '2023171009',
        'publication_id': 1,  # 使用发布实例ID
        'note': '测试publication_id提交',
        'runtime': 0.123,
        'hardware': '测试机器'
    }
    
    try:
        # 发送请求
        response = requests.post(f"{SERVER_URL}/api/submissions", 
                               data=data, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ publication_id提交成功!")
            return True
        else:
            print(f"❌ publication_id提交失败: {response.status_code}")
            try:
                error_detail = response.json().get('detail', '未知错误')
                print(f"错误详情: {error_detail}")
            except:
                pass
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"❌ 请求出错: {str(e)}")
        return False

def test_question_id():
    """测试使用question_id提交（向后兼容）"""
    print("\n=== 测试question_id提交（向后兼容）===")
    
    # 准备测试数据
    data = {
        'student_no': '2023171010',
        'question_id': 1,  # 使用原始题目ID
        'note': '测试question_id提交',
        'runtime': 0.156,
        'hardware': '测试机器2'
    }
    
    try:
        # 发送请求
        response = requests.post(f"{SERVER_URL}/api/submissions", 
                               data=data, timeout=10)
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ question_id提交成功!")
            return True
        else:
            print(f"❌ question_id提交失败: {response.status_code}")
            try:
                error_detail = response.json().get('detail', '未知错误')
                print(f"错误详情: {error_detail}")
            except:
                pass
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"❌ 请求出错: {str(e)}")
        return False

def get_publications():
    """获取发布实例列表"""
    print("\n=== 获取发布实例列表 ===")
    
    try:
        # 获取学生可见的题目列表
        response = requests.get(f"{SERVER_URL}/student/questions", timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            questions = response.json()
            print(f"找到 {len(questions)} 个可提交的题目:")
            for q in questions:
                print(f"  - ID: {q.get('id')}, Publication ID: {q.get('publication_id')}, "
                      f"Question ID: {q.get('question_id')}, Title: {q.get('title')}")
            return questions
        else:
            print(f"❌ 获取题目列表失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 请求出错: {str(e)}")
        return []

if __name__ == "__main__":
    print("开始测试publication_id和question_id的使用...")
    
    # 1. 获取可用的发布实例
    publications = get_publications()
    
    # 2. 测试publication_id
    test_publication_id()
    
    # 3. 测试question_id（向后兼容）
    test_question_id()
    
    print("\n测试完成!")
