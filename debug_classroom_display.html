<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课堂展示调试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin-bottom: 1rem;
            padding: 1rem;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .error-log {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="debug-container">
            <h1>课堂展示 ProgressBar 调试</h1>
            
            <div class="test-item">
                <h3>组件加载状态</h3>
                <p>ProgressBar 组件: {{ progressBarStatus }}</p>
                <p>window.ProgressBar: {{ windowProgressBarStatus }}</p>
                <p>Vue 版本: {{ Vue.version }}</p>
            </div>
            
            <div class="test-item">
                <h3>基础测试</h3>
                <p>进度: {{ percentage }}%</p>
                <input type="range" v-model="percentage" min="0" max="100" step="1">
                
                <div style="margin-top: 1rem;">
                    <h4>使用 progress-bar 组件:</h4>
                    <progress-bar 
                        :percentage="percentage" 
                        color="#007bff"
                        :show-text="true"
                    ></progress-bar>
                </div>
            </div>
            
            <div class="test-item">
                <h3>排行榜样式测试</h3>
                <div class="metric-item score-bar-container">
                    <span class="metric-label">分数:</span>
                    <progress-bar
                        :percentage="percentage"
                        :color="percentage > 70 ? '#28a745' : percentage > 40 ? '#ffc107' : '#dc3545'"
                        :stroke-width="25"
                        :show-text="false"
                        :animated="true"
                        :striped="percentage > 70"
                    ></progress-bar>
                    <span class="score-value">{{ (percentage * 10).toFixed(1) }}</span>
                </div>
            </div>
            
            <div class="test-item">
                <h3>错误日志</h3>
                <div id="error-log" class="error-log">
                    <!-- 错误信息将显示在这里 -->
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/components/ProgressBar.js"></script>
    <script>
        // 捕获控制台错误和警告
        const originalError = console.error;
        const originalWarn = console.warn;
        const errorLog = document.getElementById('error-log');
        
        console.error = function(...args) {
            originalError.apply(console, args);
            errorLog.innerHTML += '<span style="color: red;">[ERROR] ' + args.join(' ') + '</span><br>';
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            errorLog.innerHTML += '<span style="color: orange;">[WARN] ' + args.join(' ') + '</span><br>';
        };
        
        // 检查组件加载状态
        console.log('ProgressBar 组件检查:', typeof ProgressBar !== 'undefined' ? ProgressBar : '未定义');
        console.log('window.ProgressBar 检查:', window.ProgressBar);
        
        const { createApp, ref, computed } = Vue;

        // 确保 ProgressBar 组件已加载
        if (typeof ProgressBar === 'undefined' && typeof window.ProgressBar === 'undefined') {
            console.error('ProgressBar 组件未加载！请检查 ProgressBar.js 文件是否正确加载。');
        }

        const app = createApp({
            components: {
                'progress-bar': window.ProgressBar || ProgressBar
            },
            setup() {
                const percentage = ref(50);
                
                const progressBarStatus = computed(() => {
                    return typeof ProgressBar !== 'undefined' ? '✅ 已加载' : '❌ 未加载';
                });
                
                const windowProgressBarStatus = computed(() => {
                    return typeof window.ProgressBar !== 'undefined' ? '✅ 已加载' : '❌ 未加载';
                });
                
                return {
                    percentage,
                    progressBarStatus,
                    windowProgressBarStatus,
                    Vue
                };
            }
        });

        // 添加错误处理
        app.config.errorHandler = (err, vm, info) => {
            console.error('Vue 应用错误:', err);
            console.error('错误信息:', info);
            console.error('组件实例:', vm);
        };

        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>
