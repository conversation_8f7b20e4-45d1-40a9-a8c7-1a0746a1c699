<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能条测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .score-bar-container {
            flex: 1;
            max-width: 400px;
            min-width: 200px;
        }

        .score-bar {
            height: 25px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            margin: 10px 0;
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 12px;
            transition: width 0.8s ease;
            position: relative;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .score-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255,255,255,0.3), transparent);
            border-radius: 12px 12px 0 0;
        }

        .test-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .label {
            min-width: 100px;
            font-weight: bold;
        }

        .score-value {
            min-width: 80px;
            font-size: 1.1rem;
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>性能条显示测试</h1>
            
            <div class="test-item">
                <span class="label">第一名:</span>
                <div class="score-bar-container">
                    <div class="score-bar">
                        <div class="score-fill" :style="{ width: getScoreWidth(6833.17, maxScore) + '%' }"></div>
                    </div>
                </div>
                <span class="score-value">{{ 6833.17.toFixed(1) }}</span>
            </div>

            <div class="test-item">
                <span class="label">第二名:</span>
                <div class="score-bar-container">
                    <div class="score-bar">
                        <div class="score-fill" :style="{ width: getScoreWidth(400.0, maxScore) + '%' }"></div>
                    </div>
                </div>
                <span class="score-value">{{ 400.0.toFixed(1) }}</span>
            </div>

            <div class="test-item">
                <span class="label">测试固定宽度:</span>
                <div class="score-bar-container">
                    <div class="score-bar">
                        <div class="score-fill" style="width: 75%;"></div>
                    </div>
                </div>
                <span class="score-value">75%</span>
            </div>

            <div class="test-item">
                <span class="label">测试小宽度:</span>
                <div class="score-bar-container">
                    <div class="score-bar">
                        <div class="score-fill" style="width: 10%;"></div>
                    </div>
                </div>
                <span class="score-value">10%</span>
            </div>

            <p><strong>最大分数:</strong> {{ maxScore }}</p>
            <p><strong>第一名宽度:</strong> {{ getScoreWidth(6833.17, maxScore) }}%</p>
            <p><strong>第二名宽度:</strong> {{ getScoreWidth(400.0, maxScore) }}%</p>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;

        createApp({
            setup() {
                const maxScore = ref(6833.17);

                const getScoreWidth = (score, maxScore) => {
                    if (maxScore === 0) return 0;
                    const width = Math.min((score / maxScore) * 100, 100);
                    console.log(`Score: ${score}, MaxScore: ${maxScore}, Width: ${width}%`);
                    return width;
                };

                return {
                    maxScore,
                    getScoreWidth
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
