#!/usr/bin/env python3
"""
测试新的学生端API
"""

import requests
import json

# 服务器配置
SERVER_URL = "http://localhost:30200"

def test_student_questions():
    """测试学生端获取题目列表"""
    print("=== 测试学生端获取题目列表 ===")
    
    try:
        response = requests.get(f"{SERVER_URL}/student/questions", timeout=10)
        
        if response.status_code == 200:
            questions = response.json()
            print(f"✅ 成功获取 {len(questions)} 个题目:")
            for q in questions:
                print(f"  - 发布ID: {q['id']}, 题目ID: {q['question_id']}, 标题: {q['title']}")
            return questions
        else:
            print(f"❌ 获取失败: {response.status_code}")
            try:
                error = response.json()
                print(f"   错误详情: {error}")
            except:
                print(f"   响应内容: {response.text}")
            return []
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        return []
    except Exception as e:
        print(f"❌ 请求出错: {str(e)}")
        return []

def test_submit_with_publication_id(publication_id):
    """测试使用publication_id提交"""
    print(f"\n=== 测试使用publication_id={publication_id}提交 ===")
    
    try:
        data = {
            'publication_id': publication_id,
            'student_no': '2022001',
            'note': '测试使用发布实例ID提交'
        }
        
        response = requests.post(f"{SERVER_URL}/api/submissions", data=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 提交成功! 提交ID: {result['submission_id']}")
            return True
        else:
            print(f"❌ 提交失败: {response.status_code}")
            try:
                error = response.json()
                print(f"   错误详情: {error}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 请求出错: {str(e)}")
        return False

def test_submit_with_question_id(question_id):
    """测试使用question_id提交（向后兼容）"""
    print(f"\n=== 测试使用question_id={question_id}提交（向后兼容） ===")
    
    try:
        data = {
            'question_id': question_id,
            'student_no': '2022002',
            'note': '测试使用题目ID提交（向后兼容）'
        }
        
        response = requests.post(f"{SERVER_URL}/api/submissions", data=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 提交成功! 提交ID: {result['submission_id']}")
            return True
        else:
            print(f"❌ 提交失败: {response.status_code}")
            try:
                error = response.json()
                print(f"   错误详情: {error}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 请求出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试新的学生端API")
    print("=" * 50)
    
    # 1. 测试获取题目列表
    questions = test_student_questions()
    
    if questions:
        # 2. 测试使用publication_id提交
        first_question = questions[0]
        publication_id = first_question['id']
        question_id = first_question['question_id']
        
        success1 = test_submit_with_publication_id(publication_id)
        
        # 3. 测试使用question_id提交（向后兼容）
        success2 = test_submit_with_question_id(question_id)
        
        print("\n" + "=" * 50)
        print("🏁 测试总结:")
        print(f"  获取题目列表: {'✅ 成功' if questions else '❌ 失败'}")
        print(f"  publication_id提交: {'✅ 成功' if success1 else '❌ 失败'}")
        print(f"  question_id提交: {'✅ 成功' if success2 else '❌ 失败'}")
        
        if questions and success1 and success2:
            print("\n🎉 所有测试通过！新的API工作正常。")
        else:
            print("\n⚠️  部分测试失败，请检查服务器状态和配置。")
    else:
        print("\n⚠️  无法获取题目列表，请检查服务器状态。")

if __name__ == "__main__":
    main()
