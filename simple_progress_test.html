<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单进度条测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .progress-track {
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            border-radius: 10px;
            transition: width 0.6s ease;
            background: linear-gradient(90deg, currentColor, rgba(255,255,255,0.2));
        }
        .vue-progress-bar {
            display: flex;
            align-items: center;
            width: 100%;
        }
        .progress-text {
            margin-left: 10px;
            font-size: 14px;
            color: #606266;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>简单进度条测试</h1>
            
            <div>
                <label>进度: {{ percentage }}%</label>
                <input type="range" v-model="percentage" min="0" max="100" step="1">
            </div>
            
            <h3>内联进度条</h3>
            <div class="progress-track">
                <div class="progress-bar" :style="{ width: percentage + '%', backgroundColor: '#007bff' }"></div>
            </div>
            
            <h3>Vue组件进度条</h3>
            <simple-progress :percentage="percentage" color="#28a745"></simple-progress>
            
            <h3>调试信息</h3>
            <pre>{{ debugInfo }}</pre>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;
        
        // 简单的进度条组件
        const SimpleProgress = {
            props: {
                percentage: {
                    type: Number,
                    default: 0
                },
                color: {
                    type: String,
                    default: '#007bff'
                }
            },
            setup(props) {
                const barStyle = computed(() => ({
                    width: props.percentage + '%',
                    backgroundColor: props.color,
                    height: '100%',
                    borderRadius: '10px',
                    transition: 'width 0.6s ease'
                }));
                
                return {
                    barStyle
                };
            },
            template: `
                <div class="vue-progress-bar">
                    <div class="progress-track" style="flex: 1;">
                        <div class="progress-bar" :style="barStyle"></div>
                    </div>
                    <div class="progress-text">{{ percentage }}%</div>
                </div>
            `
        };

        createApp({
            components: {
                'simple-progress': SimpleProgress
            },
            setup() {
                const percentage = ref(50);
                
                const debugInfo = computed(() => ({
                    percentage: percentage.value,
                    Vue版本: Vue.version,
                    组件已注册: 'simple-progress' in this?.$options?.components || {}
                }));
                
                return {
                    percentage,
                    debugInfo
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
