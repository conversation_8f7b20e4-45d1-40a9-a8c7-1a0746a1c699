<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProgressBar 组件测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #eee;
            border-radius: 8px;
        }

        .test-item {
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .test-label {
            min-width: 120px;
            font-weight: bold;
        }

        .controls {
            margin-bottom: 2rem;
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        button {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }

        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="test-container">
            <h1>ProgressBar 组件测试</h1>
            
            <div class="controls">
                <label>进度值: {{ percentage }}%</label>
                <input type="range" v-model="percentage" min="0" max="100" step="1">
                <button @click="randomProgress">随机进度</button>
                <button @click="animateProgress">动画演示</button>
            </div>

            <div class="test-section">
                <h3>基础样式测试</h3>
                
                <div class="test-item">
                    <span class="test-label">默认样式:</span>
                    <progress-bar :percentage="percentage"></progress-bar>
                </div>

                <div class="test-item">
                    <span class="test-label">不显示文字:</span>
                    <progress-bar :percentage="percentage" :show-text="false"></progress-bar>
                </div>

                <div class="test-item">
                    <span class="test-label">文字内嵌:</span>
                    <progress-bar :percentage="percentage" :text-inside="true"></progress-bar>
                </div>

                <div class="test-item">
                    <span class="test-label">自定义颜色:</span>
                    <progress-bar :percentage="percentage" color="#e74c3c"></progress-bar>
                </div>

                <div class="test-item">
                    <span class="test-label">更粗的条:</span>
                    <progress-bar :percentage="percentage" :stroke-width="30" color="#2ecc71"></progress-bar>
                </div>
            </div>

            <div class="test-section">
                <h3>状态样式测试</h3>
                
                <div class="test-item">
                    <span class="test-label">成功状态:</span>
                    <progress-bar :percentage="percentage" status="success"></progress-bar>
                </div>

                <div class="test-item">
                    <span class="test-label">警告状态:</span>
                    <progress-bar :percentage="percentage" status="warning"></progress-bar>
                </div>

                <div class="test-item">
                    <span class="test-label">错误状态:</span>
                    <progress-bar :percentage="percentage" status="exception"></progress-bar>
                </div>
            </div>

            <div class="test-section">
                <h3>动画效果测试</h3>
                
                <div class="test-item">
                    <span class="test-label">条纹效果:</span>
                    <progress-bar :percentage="percentage" :striped="true" color="#f39c12"></progress-bar>
                </div>

                <div class="test-item">
                    <span class="test-label">动画条纹:</span>
                    <progress-bar :percentage="percentage" :striped="true" :animated="true" color="#9b59b6"></progress-bar>
                </div>

                <div class="test-item">
                    <span class="test-label">自定义格式:</span>
                    <progress-bar
                        :percentage="percentage"
                        :format="(p) => `${p}% 完成`"
                        color="#34495e"
                    ></progress-bar>
                </div>
            </div>

            <div class="test-section">
                <h3>排行榜样式测试</h3>
                
                <div class="leaderboard-item" style="display: flex; align-items: center; padding: 1rem; border: 1px solid #eee; border-radius: 8px;">
                    <div class="rank-badge rank-1" style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #ffd700, #ffed4e); display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                        <i class="fas fa-crown" style="color: #b8860b;"></i>
                    </div>
                    
                    <div class="student-info" style="flex: 1; margin-right: 1rem;">
                        <div class="student-name" style="font-weight: bold;">张三</div>
                        <div class="student-details" style="color: #666; font-size: 0.9rem;">学号: 2021001 | 硬件: RTX 4090</div>
                    </div>
                    
                    <div class="performance-info" style="flex: 2; margin-right: 1rem;">
                        <div class="score-bar-container">
                            <div class="score-label">{{ Math.round(percentage * 68.33) }} 分</div>
                            <progress-bar
                                :percentage="percentage"
                                color="#28a745"
                                :stroke-width="20"
                                :show-text="false"
                                :animated="true"
                                :striped="true"
                                status="success"
                            ></progress-bar>
                            <div class="runtime-label">0.146s</div>
                        </div>
                    </div>
                    
                    <div class="submission-time" style="color: #666; font-size: 0.85rem;">
                        2024-01-15 14:30
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/components/ProgressBar.js"></script>
    <script>
        const { createApp, ref, onMounted } = Vue;

        createApp({
            components: {
                'progress-bar': ProgressBar
            },
            setup() {
                const percentage = ref(75);

                const randomProgress = () => {
                    percentage.value = Math.floor(Math.random() * 101);
                };

                const animateProgress = () => {
                    percentage.value = 0;
                    let current = 0;
                    const interval = setInterval(() => {
                        current += 2;
                        percentage.value = current;
                        if (current >= 100) {
                            clearInterval(interval);
                        }
                    }, 50);
                };

                return {
                    percentage,
                    randomProgress,
                    animateProgress
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
