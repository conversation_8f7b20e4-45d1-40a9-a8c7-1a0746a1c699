#!/usr/bin/env python3
"""
测试Docker环境中的性能排行榜功能
"""

import requests
import json
import time

def test_docker_leaderboard():
    """测试Docker环境中的排行榜API"""
    base_url = "http://localhost:30200"
    
    print("🔍 测试Docker环境中的性能排行榜功能...")
    
    # 1. 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到Docker容器: {e}")
        return False
    
    # 2. 测试排行榜API
    try:
        response = requests.get(f"{base_url}/display/leaderboard?question_id=1&limit=10", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ 排行榜API正常工作")
            print(f"📊 排行榜数据: {len(data.get('leaderboard', []))} 条记录")
            
            # 显示排行榜数据
            for i, item in enumerate(data.get('leaderboard', [])[:3]):
                print(f"   {i+1}. {item['student_no']} - 分数: {item['score']:.1f} - 时间: {item['runtime']:.3f}s")
                if item.get('hardware'):
                    print(f"      硬件: {item['hardware']}")
            
            return True
        else:
            print(f"❌ 排行榜API失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 排行榜API请求失败: {e}")
        return False

def test_classroom_display():
    """测试全屏显示页面"""
    base_url = "http://localhost:30200"
    
    print("\n🖥️  测试全屏显示页面...")
    
    try:
        response = requests.get(f"{base_url}/static/classroom_display.html?class_id=1&question_id=1", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查关键元素
            checks = [
                ("控制面板", "control-panel" in content),
                ("性能排行榜选项", "性能排行榜" in content),
                ("显示选项", "displayOptions" in content),
                ("Vue.js", "vue.global.js" in content),
                ("Font Awesome", "font-awesome" in content)
            ]
            
            all_passed = True
            for check_name, passed in checks:
                if passed:
                    print(f"   ✅ {check_name}")
                else:
                    print(f"   ❌ {check_name}")
                    all_passed = False
            
            return all_passed
        else:
            print(f"❌ 全屏显示页面加载失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 全屏显示页面请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🐳 Docker环境性能排行榜功能测试")
    print("=" * 50)
    
    # 等待Docker容器完全启动
    print("⏳ 等待Docker容器启动...")
    time.sleep(5)
    
    # 运行测试
    api_test = test_docker_leaderboard()
    display_test = test_classroom_display()
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"   排行榜API: {'✅ 通过' if api_test else '❌ 失败'}")
    print(f"   全屏显示: {'✅ 通过' if display_test else '❌ 失败'}")
    
    if api_test and display_test:
        print("\n🎉 所有测试通过！Docker环境中的性能排行榜功能正常工作。")
        print("\n📖 使用说明:")
        print("   1. 访问: http://localhost:30200/static/classroom_display.html?class_id=1&question_id=1")
        print("   2. 查看右上角控制面板")
        print("   3. 选择'性能排行榜'查看性能数据")
        print("   4. 使用复选框控制显示选项")
        return True
    else:
        print("\n❌ 部分测试失败，请检查Docker环境配置。")
        return False

if __name__ == "__main__":
    main()
