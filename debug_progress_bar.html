<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProgressBar 调试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin-bottom: 1rem;
            padding: 1rem;
            border: 1px solid #eee;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="debug-container">
            <h1>ProgressBar 组件调试</h1>
            
            <div class="test-item">
                <h3>组件加载状态</h3>
                <p>ProgressBar 组件: {{ typeof ProgressBar !== 'undefined' ? '✅ 已加载' : '❌ 未加载' }}</p>
                <p>Vue 版本: {{ Vue.version }}</p>
            </div>
            
            <div class="test-item">
                <h3>基础测试</h3>
                <p>进度: {{ percentage }}%</p>
                <input type="range" v-model="percentage" min="0" max="100" step="1">
                
                <div style="margin-top: 1rem;">
                    <progress-bar 
                        :percentage="percentage" 
                        color="#007bff"
                        :show-text="true"
                    ></progress-bar>
                </div>
            </div>
            
            <div class="test-item">
                <h3>错误信息</h3>
                <div id="error-log" style="background: #f8f9fa; padding: 1rem; border-radius: 4px; font-family: monospace; font-size: 12px;">
                    <!-- 错误信息将显示在这里 -->
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/components/ProgressBar.js"></script>
    <script>
        // 捕获控制台错误
        const originalError = console.error;
        const errorLog = document.getElementById('error-log');
        
        console.error = function(...args) {
            originalError.apply(console, args);
            errorLog.innerHTML += args.join(' ') + '<br>';
        };
        
        const originalWarn = console.warn;
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            errorLog.innerHTML += '<span style="color: orange;">' + args.join(' ') + '</span><br>';
        };
        
        // 检查组件是否加载
        console.log('ProgressBar 组件:', typeof ProgressBar !== 'undefined' ? ProgressBar : '未定义');
        console.log('window.ProgressBar:', window.ProgressBar);
        
        const { createApp, ref } = Vue;

        createApp({
            components: {
                'progress-bar': window.ProgressBar || ProgressBar
            },
            setup() {
                const percentage = ref(50);
                
                return {
                    percentage,
                    ProgressBar: window.ProgressBar,
                    Vue
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
