#!/usr/bin/env python3
"""
验证性能条显示的脚本
"""

import requests
import time

def test_score_bars():
    """测试性能条显示"""
    base_url = "http://localhost:30200"
    
    print("🔍 验证性能条显示功能")
    print("=" * 50)
    
    # 1. 测试固定宽度页面
    print("1. 测试固定宽度性能条...")
    try:
        response = requests.get(f"{base_url}/static/fixed_width_test.html", timeout=10)
        if response.status_code == 200:
            print("   ✅ 固定宽度测试页面加载成功")
            content = response.text
            if "score-fill" in content and "width: 100%" in content:
                print("   ✅ 固定宽度样式存在")
            else:
                print("   ❌ 固定宽度样式缺失")
        else:
            print(f"   ❌ 固定宽度测试页面加载失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 固定宽度测试失败: {e}")
    
    # 2. 测试主页面
    print("\n2. 测试主页面性能条...")
    try:
        response = requests.get(f"{base_url}/static/classroom_display.html", timeout=10)
        if response.status_code == 200:
            print("   ✅ 主页面加载成功")
            content = response.text
            
            # 检查关键元素
            checks = [
                ("性能条容器", "score-bar-container" in content),
                ("性能条样式", "score-bar" in content),
                ("性能条填充", "score-fill" in content),
                ("绿色渐变", "linear-gradient(90deg, #28a745, #20c997)" in content),
                ("简化宽度计算", "item.score > 6000" in content),
                ("Vue.js绑定", ":style" in content)
            ]
            
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"   {status} {check_name}")
        else:
            print(f"   ❌ 主页面加载失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 主页面测试失败: {e}")
    
    # 3. 测试API数据
    print("\n3. 测试排行榜数据...")
    try:
        response = requests.get(f"{base_url}/display/leaderboard?question_id=1&limit=10", timeout=10)
        if response.status_code == 200:
            data = response.json()
            leaderboard = data.get('leaderboard', [])
            print(f"   ✅ 排行榜API正常，{len(leaderboard)} 条记录")
            
            if leaderboard:
                for i, item in enumerate(leaderboard):
                    score = item['score']
                    if score > 6000:
                        expected_width = "100%"
                    elif score > 1000:
                        expected_width = "50%"
                    elif score > 300:
                        expected_width = "15%"
                    else:
                        expected_width = "5%"
                    
                    print(f"   {i+1}. {item['student_no']}: {score:.1f}分 → 预期宽度: {expected_width}")
            else:
                print("   ❌ 没有排行榜数据")
        else:
            print(f"   ❌ 排行榜API失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 排行榜API测试失败: {e}")
    
    print("\n" + "=" * 50)
    print("📋 验证总结:")
    print("   如果您在浏览器中看到绿色性能条，说明功能正常")
    print("   如果看不到，请检查浏览器开发者工具的控制台错误")
    
    print("\n🔗 测试链接:")
    print("   固定宽度测试: http://localhost:30200/static/fixed_width_test.html")
    print("   全屏显示测试: http://localhost:30200/static/classroom_display.html?class_id=1&question_id=1")
    
    print("\n💡 调试建议:")
    print("   1. 打开浏览器开发者工具 (F12)")
    print("   2. 切换到'性能排行榜'视图")
    print("   3. 检查控制台是否有JavaScript错误")
    print("   4. 检查Elements面板中的score-fill元素是否有正确的width样式")

if __name__ == "__main__":
    print("⏳ 等待Docker容器启动...")
    time.sleep(3)
    test_score_bars()
