#!/usr/bin/env python3
"""
修复 ProgressBar 组件问题的脚本
"""

import os
import sys
import subprocess
import webbrowser
import time

def check_files():
    """检查必要的文件是否存在"""
    print("🔍 检查文件...")
    
    files_to_check = [
        'static/js/components/ProgressBar.js',
        'static/classroom_display.html',
        'debug_classroom_display.html',
        'static/css/styles.css'
    ]
    
    missing_files = []
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - 文件不存在")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n❌ 缺少 {len(missing_files)} 个文件，请检查文件路径")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def start_server():
    """启动测试服务器"""
    print("\n🚀 启动测试服务器...")
    
    try:
        # 尝试启动 Python 服务器
        if sys.version_info[0] >= 3:
            process = subprocess.Popen(['python', '-m', 'http.server', '8000'], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE)
        else:
            process = subprocess.Popen(['python', '-m', 'SimpleHTTPServer', '8000'], 
                                     stdout=subprocess.PIPE, 
                                     stderr=subprocess.PIPE)
        
        print("✅ 服务器启动成功，端口: 8000")
        return process
    
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return None

def test_progressbar():
    """测试 ProgressBar 组件"""
    print("\n🧪 测试 ProgressBar 组件...")
    
    test_urls = [
        ("调试页面", "http://localhost:8000/debug_classroom_display.html"),
        ("课堂展示", "http://localhost:8000/static/classroom_display.html?question_id=1&class_id=1"),
        ("组件测试", "http://localhost:8000/test_progress_bar.html")
    ]
    
    print("\n📋 测试步骤:")
    for i, (name, url) in enumerate(test_urls, 1):
        print(f"{i}. {name}: {url}")
    
    print("\n🔗 自动打开调试页面...")
    webbrowser.open(test_urls[0][1])
    
    return test_urls

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 ProgressBar 组件问题修复工具")
    print("=" * 60)
    
    # 检查文件
    if not check_files():
        sys.exit(1)
    
    # 启动服务器
    server_process = start_server()
    if not server_process:
        sys.exit(1)
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 测试组件
    test_urls = test_progressbar()
    
    print("\n" + "=" * 60)
    print("📋 故障排除指南:")
    print("=" * 60)
    
    print("\n1. 🔍 检查调试页面:")
    print("   - 打开调试页面，查看组件加载状态")
    print("   - 检查错误日志区域是否有错误信息")
    print("   - 确认进度条是否正常显示")
    
    print("\n2. 🌐 清除浏览器缓存:")
    print("   - 按 Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac) 强制刷新")
    print("   - 或者打开开发者工具，右键刷新按钮选择'清空缓存并硬性重新加载'")
    
    print("\n3. 🔧 检查控制台错误:")
    print("   - 打开浏览器开发者工具 (F12)")
    print("   - 查看 Console 标签页是否有 JavaScript 错误")
    print("   - 查看 Network 标签页确认 ProgressBar.js 文件是否正确加载")
    
    print("\n4. 📁 验证文件路径:")
    print("   - 确认 /static/js/components/ProgressBar.js 可以访问")
    print("   - 检查文件内容是否完整")
    
    print("\n✅ 如果调试页面显示正常，问题可能是浏览器缓存导致的")
    print("❌ 如果调试页面也有问题，请检查 ProgressBar.js 文件是否正确")
    
    print(f"\n🔗 测试链接:")
    for name, url in test_urls:
        print(f"   {name}: {url}")
    
    print("\n按 Ctrl+C 停止服务器")
    
    try:
        server_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务器...")
        server_process.terminate()
        server_process.wait()
        print("✅ 服务器已停止")

if __name__ == "__main__":
    main()
