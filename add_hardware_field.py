#!/usr/bin/env python3
"""
为提交表添加hardware字段的数据库迁移脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db.session import SessionLocal

def add_hardware_field():
    """为提交表添加hardware字段"""
    print("开始添加hardware字段...")
    
    db = SessionLocal()
    try:
        # 检查字段是否已存在（SQLite方式）
        try:
            result = db.execute(text("PRAGMA table_info(submissions)"))
            columns = [row[1] for row in result.fetchall()]  # row[1] 是列名
            
            if 'hardware' in columns:
                print("✅ hardware字段已存在，跳过添加")
                return
        except Exception as e:
            print(f"检查字段时出错: {e}")
            # 继续尝试添加字段
        
        # 添加hardware字段
        db.execute(text("""
            ALTER TABLE submissions 
            ADD COLUMN hardware TEXT
        """))
        
        db.commit()
        print("✅ 成功添加hardware字段")
        
        # 验证添加结果
        result = db.execute(text("SELECT COUNT(*) as total FROM submissions WHERE hardware IS NOT NULL"))
        row = result.fetchone()
        if row:
            print(f"📊 提交统计: 包含硬件信息的提交数量: {row.total}")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 添加字段失败: {e}")
        raise
    finally:
        db.close()

def main():
    """主函数"""
    print("🔧 硬件字段迁移工具")
    print("=" * 50)
    
    try:
        add_hardware_field()
        print("\n🎉 迁移完成！")
    except Exception as e:
        print(f"\n💥 迁移失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
