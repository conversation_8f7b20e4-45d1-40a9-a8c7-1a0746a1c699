#!/usr/bin/env python3
"""
检查题目和发布数据的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models import Question, QuestionPublication, Class

def check_question_data():
    """检查题目数据"""
    db = SessionLocal()
    try:
        print("=== 题目数据检查 ===")
        
        # 检查题目ID=15
        question_15 = db.query(Question).filter(Question.id == 15).first()
        if question_15:
            print(f"\n题目ID=15:")
            print(f"  标题: {question_15.title}")
            print(f"  描述: {question_15.description}")
            print(f"  是否开放: {question_15.is_open}")
            print(f"  是否发布: {question_15.is_published}")
            print(f"  发布班级: {question_15.published_classes}")
            print(f"  创建时间: {question_15.created_at}")
        else:
            print("题目ID=15不存在")
        
        print(f"\n=== 题目发布记录 ===")
        publications = db.query(QuestionPublication).filter(
            QuestionPublication.question_id == 15
        ).all()
        
        if publications:
            for pub in publications:
                class_obj = db.query(Class).filter(Class.id == pub.class_id).first()
                print(f"发布记录ID={pub.id}:")
                print(f"  题目ID: {pub.question_id}")
                print(f"  班级ID: {pub.class_id}")
                print(f"  班级名称: {class_obj.name if class_obj else '未知'}")
                print(f"  是否激活: {pub.is_active}")
                print(f"  发布时间: {pub.published_at}")
                print()
        else:
            print("题目ID=15没有发布记录")
        
        print(f"\n=== 所有题目概览 ===")
        questions = db.query(Question).order_by(Question.id).all()
        for q in questions:
            print(f"ID={q.id}: {q.title} (开放:{q.is_open}, 发布:{q.is_published})")
        
        print(f"\n=== 所有发布记录概览 ===")
        all_publications = db.query(QuestionPublication).order_by(QuestionPublication.id).all()
        for pub in all_publications:
            question = db.query(Question).filter(Question.id == pub.question_id).first()
            class_obj = db.query(Class).filter(Class.id == pub.class_id).first()
            print(f"发布ID={pub.id}: 题目{pub.question_id}({question.title if question else '未知'}) -> 班级{pub.class_id}({class_obj.name if class_obj else '未知'}) (激活:{pub.is_active})")
            
    except Exception as e:
        print(f"检查失败: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    check_question_data()
