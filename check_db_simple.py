#!/usr/bin/env python3
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models import Question, QuestionPublication, Class

def main():
    db = SessionLocal()
    try:
        print("=== 数据库状态检查 ===")
        
        # 检查班级
        classes = db.query(Class).all()
        print(f"班级数量: {len(classes)}")
        for cls in classes:
            print(f"  - ID: {cls.id}, 名称: {cls.name}, 激活: {cls.is_active}")
        
        # 检查题目
        questions = db.query(Question).all()
        print(f"\n题目数量: {len(questions)}")
        for q in questions:
            print(f"  - ID: {q.id}, 标题: {q.title}, 开放: {q.is_open}")
        
        # 检查发布记录
        publications = db.query(QuestionPublication).all()
        print(f"\n发布记录数量: {len(publications)}")
        for pub in publications:
            question = db.query(Question).filter(Question.id == pub.question_id).first()
            class_obj = db.query(Class).filter(Class.id == pub.class_id).first()
            print(f"  - 发布ID: {pub.id}, 题目: {question.title if question else 'N/A'}, 班级: {class_obj.name if class_obj else 'N/A'}, 激活: {pub.is_active}")
        
    except Exception as e:
        print(f"错误: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    main()
