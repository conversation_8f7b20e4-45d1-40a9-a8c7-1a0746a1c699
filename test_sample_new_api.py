#!/usr/bin/env python3
"""
测试sample中的新API使用方式
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'sample'))

# 导入sample中的函数
from test import get_available_questions, submit_answer

def test_get_questions():
    """测试获取题目列表"""
    print("=== 测试获取题目列表 ===")
    
    questions = get_available_questions()
    
    if questions:
        print(f"✅ 成功获取 {len(questions)} 个题目")
        return questions
    else:
        print("❌ 获取题目列表失败")
        return []

def test_submit_with_publication_id():
    """测试使用publication_id提交"""
    print("\n=== 测试使用publication_id提交 ===")
    
    # 首先获取可用题目
    questions = get_available_questions()
    if not questions:
        print("❌ 没有可用题目")
        return False
    
    # 使用第一个题目的publication_id
    first_question = questions[0]
    publication_id = first_question['id']
    
    print(f"使用发布ID: {publication_id} ({first_question['title']})")
    
    # 提交
    result = submit_answer(
        student_no='test_student_001',
        publication_id=publication_id,
        note='测试使用publication_id提交'
    )
    
    return result['success']

def test_submit_with_question_id():
    """测试使用question_id提交（向后兼容）"""
    print("\n=== 测试使用question_id提交（向后兼容） ===")
    
    # 使用已知的题目ID
    question_id = 15  # 最终测试题目
    
    print(f"使用题目ID: {question_id}")
    
    # 提交
    result = submit_answer(
        student_no='test_student_002',
        question_id=question_id,
        note='测试使用question_id提交（向后兼容）'
    )
    
    return result['success']

def main():
    """主测试函数"""
    print("🧪 开始测试sample中的新API使用方式")
    print("=" * 60)
    
    # 1. 测试获取题目列表
    questions = test_get_questions()
    
    # 2. 测试使用publication_id提交
    success1 = test_submit_with_publication_id() if questions else False
    
    # 3. 测试使用question_id提交（向后兼容）
    success2 = test_submit_with_question_id()
    
    print("\n" + "=" * 60)
    print("🏁 测试总结:")
    print(f"  获取题目列表: {'✅ 成功' if questions else '❌ 失败'} ({len(questions)} 个题目)")
    print(f"  publication_id提交: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"  question_id提交: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if questions and success1 and success2:
        print("\n🎉 所有测试通过！sample代码工作正常。")
        print("\n💡 使用建议:")
        print("  - 推荐使用 publication_id 方式提交")
        print("  - question_id 方式仍然支持，用于向后兼容")
        print("  - 可以通过 get_available_questions() 获取可用题目列表")
    else:
        print("\n⚠️  部分测试失败，请检查网络连接和服务器状态。")

if __name__ == "__main__":
    main()
