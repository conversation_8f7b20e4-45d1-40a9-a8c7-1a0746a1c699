#!/usr/bin/env python3
"""
测试所有修复是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models import Student, Submission
from sqlalchemy import text

def test_student_active_field():
    """测试学生激活状态字段"""
    print("=== 测试学生激活状态字段 ===")
    
    db = SessionLocal()
    try:
        # 检查字段是否存在
        result = db.execute(text("PRAGMA table_info(students)"))
        columns = [row[1] for row in result.fetchall()]
        
        if 'is_active' in columns:
            print("✅ is_active字段存在")
        else:
            print("❌ is_active字段不存在")
            return False
        
        # 测试查询学生
        students = db.query(Student).limit(3).all()
        print(f"✅ 成功查询到 {len(students)} 个学生")
        
        for student in students:
            print(f"  - {student.name}: 激活状态 = {student.is_active}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        db.close()

def test_hardware_field():
    """测试硬件字段"""
    print("\n=== 测试硬件字段 ===")
    
    db = SessionLocal()
    try:
        # 检查字段是否存在
        result = db.execute(text("PRAGMA table_info(submissions)"))
        columns = [row[1] for row in result.fetchall()]
        
        if 'hardware' in columns:
            print("✅ hardware字段存在")
        else:
            print("❌ hardware字段不存在")
            return False
        
        # 测试查询提交
        submissions = db.query(Submission).limit(3).all()
        print(f"✅ 成功查询到 {len(submissions)} 个提交")
        
        for submission in submissions:
            print(f"  - 提交ID {submission.id}: 硬件信息 = {submission.hardware}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        db.close()

def test_student_activation_logic():
    """测试学生激活逻辑"""
    print("\n=== 测试学生激活逻辑 ===")
    
    db = SessionLocal()
    try:
        # 找一个学生并测试激活状态切换
        student = db.query(Student).first()
        if not student:
            print("❌ 没有找到学生")
            return False
        
        original_status = student.is_active
        print(f"学生 {student.name} 原始状态: {original_status}")
        
        # 切换状态
        student.is_active = not original_status
        db.commit()
        
        # 验证状态已更改
        db.refresh(student)
        new_status = student.is_active
        print(f"切换后状态: {new_status}")
        
        if new_status != original_status:
            print("✅ 状态切换成功")
        else:
            print("❌ 状态切换失败")
            return False
        
        # 恢复原始状态
        student.is_active = original_status
        db.commit()
        print(f"恢复原始状态: {original_status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        db.close()

def main():
    """主测试函数"""
    print("🧪 开始测试所有修复")
    print("=" * 50)
    
    tests = [
        test_student_active_field,
        test_hardware_field,
        test_student_activation_logic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 测试总结:")
    print(f"  通过: {passed}/{total}")
    
    if passed == total:
        print("\n🎉 所有测试通过！修复成功。")
    else:
        print(f"\n⚠️  {total - passed} 个测试失败，请检查修复。")

if __name__ == "__main__":
    main()
