#!/usr/bin/env python3
"""
创建测试性能数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models import Submission
from sqlalchemy import text

def create_test_performance_data():
    """创建测试性能数据"""
    print("=== 创建测试性能数据 ===")
    
    db = SessionLocal()
    try:
        # 更新现有提交，添加运行时间和硬件信息
        test_data = [
            {"id": 1, "runtime": 2.5, "hardware": "Intel i7-12700K + RTX 3080"},
            {"id": 2, "runtime": 1.8, "hardware": "AMD Ryzen 9 5900X + RTX 3070"},
            {"id": 3, "runtime": 3.2, "hardware": "Intel i5-11400 + GTX 1660"},
        ]
        
        updated_count = 0
        for data in test_data:
            submission = db.query(Submission).filter(Submission.id == data["id"]).first()
            if submission:
                submission.runtime = data["runtime"]
                submission.hardware = data["hardware"]
                updated_count += 1
                print(f"✅ 更新提交ID {data['id']}: 运行时间={data['runtime']}s, 硬件={data['hardware']}")
        
        db.commit()
        print(f"✅ 成功更新 {updated_count} 个提交的性能数据")
        
        # 验证数据
        submissions = db.query(Submission).filter(Submission.runtime.isnot(None)).all()
        print(f"\n验证: 找到 {len(submissions)} 个包含运行时间的提交:")
        for submission in submissions:
            score = 1000.0 / submission.runtime if submission.runtime > 0 else 0
            print(f"  - ID {submission.id}: 运行时间={submission.runtime}s, 性能分数={score:.1f}, 硬件={submission.hardware}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    create_test_performance_data()
