#!/usr/bin/env python3
"""
最终测试脚本 - 验证性能排行榜的所有功能
"""

import requests
import json
import time

def test_final_leaderboard():
    """测试最终的性能排行榜功能"""
    base_url = "http://localhost:30200"
    
    print("🎯 最终性能排行榜功能测试")
    print("=" * 60)
    
    # 1. 测试API数据
    try:
        response = requests.get(f"{base_url}/display/leaderboard?question_id=1&limit=10", timeout=10)
        if response.status_code == 200:
            data = response.json()
            leaderboard = data.get('leaderboard', [])
            
            print("✅ 排行榜API正常工作")
            print(f"📊 数据记录数: {len(leaderboard)}")
            
            if leaderboard:
                max_score = max(item['score'] for item in leaderboard)
                print(f"🏆 最高分数: {max_score:.1f}")
                
                print("\n📋 排行榜详情:")
                for i, item in enumerate(leaderboard):
                    width_percent = (item['score'] / max_score) * 100 if max_score > 0 else 0
                    print(f"   {i+1}. {item['student_no']}")
                    print(f"      分数: {item['score']:.1f} (性能条宽度: {width_percent:.1f}%)")
                    print(f"      时间: {item['runtime']:.3f}s")
                    if item.get('hardware'):
                        print(f"      硬件: {item['hardware']}")
                    print()
                
                return True
            else:
                print("❌ 没有排行榜数据")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_display_page():
    """测试全屏显示页面"""
    base_url = "http://localhost:30200"
    
    print("🖥️  测试全屏显示页面功能")
    print("-" * 40)
    
    try:
        response = requests.get(f"{base_url}/static/classroom_display.html?class_id=1&question_id=1", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查关键功能
            checks = [
                ("控制面板", "control-panel" in content),
                ("性能排行榜选项", "性能排行榜" in content),
                ("显示选项控制", "displayOptions" in content),
                ("分数条样式", "score-bar" in content),
                ("性能条填充", "score-fill" in content),
                ("硬件值样式", "hardware-value" in content),
                ("Vue.js框架", "vue.global.js" in content),
                ("数学计算", "Math.min" in content)
            ]
            
            all_passed = True
            for check_name, passed in checks:
                status = "✅" if passed else "❌"
                print(f"   {status} {check_name}")
                if not passed:
                    all_passed = False
            
            return all_passed
        else:
            print(f"❌ 页面加载失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 页面测试失败: {e}")
        return False

def test_style_optimizations():
    """测试样式优化"""
    print("\n🎨 测试样式优化")
    print("-" * 40)
    
    base_url = "http://localhost:30200"
    
    try:
        response = requests.get(f"{base_url}/static/classroom_display.html", timeout=10)
        if response.status_code == 200:
            content = response.text
            
            # 检查样式优化
            style_checks = [
                ("性能条容器宽度", "max-width: 500px" in content and "min-width: 200px" in content),
                ("绿色渐变背景", "linear-gradient(90deg, #28a745, #20c997)" in content),
                ("硬件信息压缩", "hardware-value" in content and "max-width: 150px" in content),
                ("时间信息压缩", "min-width: 60px" in content),
                ("性能条高度", "height: 25px" in content),
                ("圆角设计", "border-radius: 12px" in content),
                ("阴影效果", "box-shadow" in content),
                ("文本溢出处理", "text-overflow: ellipsis" in content)
            ]
            
            all_passed = True
            for check_name, passed in style_checks:
                status = "✅" if passed else "❌"
                print(f"   {status} {check_name}")
                if not passed:
                    all_passed = False
            
            return all_passed
        else:
            print(f"❌ 样式检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 样式测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 启动最终功能测试...")
    time.sleep(3)
    
    # 运行所有测试
    api_test = test_final_leaderboard()
    display_test = test_display_page()
    style_test = test_style_optimizations()
    
    print("\n" + "=" * 60)
    print("📊 最终测试结果:")
    print(f"   排行榜API: {'✅ 通过' if api_test else '❌ 失败'}")
    print(f"   全屏显示: {'✅ 通过' if display_test else '❌ 失败'}")
    print(f"   样式优化: {'✅ 通过' if style_test else '❌ 失败'}")
    
    if api_test and display_test and style_test:
        print("\n🎉 所有测试通过！")
        print("\n📖 功能特性:")
        print("   ✨ 性能排行榜已移至全屏显示")
        print("   ✨ 移除了学生姓名显示")
        print("   ✨ 单行紧凑布局")
        print("   ✨ 突出的绿色性能条")
        print("   ✨ 压缩的硬件和时间显示")
        print("   ✨ 可控制的显示选项")
        
        print("\n🔗 访问链接:")
        print("   http://localhost:30200/static/classroom_display.html?class_id=1&question_id=1")
        
        print("\n💡 使用说明:")
        print("   1. 右上角控制面板切换到'性能排行榜'")
        print("   2. 使用复选框控制显示内容")
        print("   3. 绿色性能条突出显示性能差异")
        print("   4. 硬件信息自动截断并显示完整tooltip")
        
        return True
    else:
        print("\n❌ 部分测试失败，请检查配置")
        return False

if __name__ == "__main__":
    main()
