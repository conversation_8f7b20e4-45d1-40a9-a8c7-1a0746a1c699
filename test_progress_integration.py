#!/usr/bin/env python3
"""
测试ProgressBar组件集成的脚本
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def main():
    print("🚀 启动ProgressBar组件集成测试...")
    
    # 检查是否在正确的目录
    if not Path("app/main.py").exists():
        print("❌ 请在项目根目录运行此脚本")
        sys.exit(1)
    
    try:
        # 启动服务器
        print("📡 启动开发服务器...")
        server_process = subprocess.Popen([
            sys.executable, "run_server.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 测试页面列表
        test_pages = [
            ("ProgressBar组件测试", "http://localhost:8000/test_progress_bar.html"),
            ("管理端排行榜", "http://localhost:8000/static/index.html"),
            ("课堂展示排行榜", "http://localhost:8000/static/classroom_display.html?question_id=1&class_ids=1"),
            ("调试排行榜", "http://localhost:8000/debug_leaderboard.html")
        ]
        
        print("\n🌐 可用的测试页面:")
        for i, (name, url) in enumerate(test_pages, 1):
            print(f"  {i}. {name}: {url}")
        
        print("\n📋 测试步骤:")
        print("1. 首先访问 ProgressBar组件测试 页面，验证组件基本功能")
        print("2. 然后访问管理端排行榜，查看硬件性能排行榜中的新进度条")
        print("3. 访问课堂展示排行榜，查看实时排行榜中的进度条效果")
        print("4. 检查进度条是否:")
        print("   - 正确显示百分比宽度")
        print("   - 根据分数显示不同颜色")
        print("   - 前三名显示条纹效果")
        print("   - 有平滑的动画过渡")
        
        # 自动打开测试页面
        print(f"\n🔗 自动打开测试页面...")
        webbrowser.open(test_pages[0][1])
        
        print("\n✅ 服务器已启动，请在浏览器中测试ProgressBar组件")
        print("按 Ctrl+C 停止服务器")
        
        # 等待用户中断
        try:
            server_process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务器...")
            server_process.terminate()
            server_process.wait()
            print("✅ 服务器已停止")
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
