<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能排行榜测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            margin: 0;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .leaderboard-item {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
            min-height: 60px;
        }

        .rank-badge {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
            flex-shrink: 0;
        }

        .rank-1 {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #b8860b;
        }

        .rank-2 {
            background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
            color: #666;
        }

        .rank-3 {
            background: linear-gradient(135deg, #cd7f32, #daa520);
            color: #8b4513;
        }

        .rank-other {
            background: #f8f9fa;
            color: #666;
            border: 2px solid #dee2e6;
        }

        .student-info-leaderboard {
            min-width: 120px;
            flex-shrink: 0;
        }

        .student-details {
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }

        .performance-metrics {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 1;
        }

        .metric-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .score-bar-container {
            flex: 1;
            max-width: 500px;
            min-width: 200px;
        }

        .score-bar {
            height: 25px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 12px;
            transition: width 0.8s ease;
            position: relative;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .score-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255,255,255,0.3), transparent);
            border-radius: 12px 12px 0 0;
        }

        .metric-label {
            font-weight: bold;
            min-width: 70px;
            font-size: 0.9rem;
            color: #555;
        }

        .metric-value {
            min-width: 80px;
            font-size: 1rem;
            color: #333;
            font-weight: 600;
        }

        .score-value {
            min-width: 80px;
            font-size: 1.1rem;
            color: #28a745;
            font-weight: bold;
        }

        .crown, .medal, .award {
            font-size: 1.2rem;
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>🏆 性能排行榜测试 - 优化后样式</h1>
        
        <!-- 第一名 -->
        <div class="leaderboard-item">
            <div class="rank-badge rank-1">
                <i class="fas fa-crown crown"></i>
            </div>
            <div class="student-info-leaderboard">
                <div class="student-details">2023171040</div>
            </div>
            <div class="performance-metrics">
                <div class="metric-item score-bar-container">
                    <span class="metric-label">分数:</span>
                    <div class="score-bar">
                        <div class="score-fill" style="width: 100%;"></div>
                    </div>
                    <span class="score-value">6833.2</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">时间:</span>
                    <span class="metric-value">0.146s</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">硬件:</span>
                    <span class="metric-value">Intel i7-12700K + RTX 3080</span>
                </div>
            </div>
        </div>

        <!-- 第二名 -->
        <div class="leaderboard-item">
            <div class="rank-badge rank-2">
                <i class="fas fa-medal medal"></i>
            </div>
            <div class="student-info-leaderboard">
                <div class="student-details">2022003</div>
            </div>
            <div class="performance-metrics">
                <div class="metric-item score-bar-container">
                    <span class="metric-label">分数:</span>
                    <div class="score-bar">
                        <div class="score-fill" style="width: 5.9%;"></div>
                    </div>
                    <span class="score-value">400.0</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">时间:</span>
                    <span class="metric-value">2.500s</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">硬件:</span>
                    <span class="metric-value">Intel i7-12700K + RTX 3080</span>
                </div>
            </div>
        </div>

        <!-- 第三名 -->
        <div class="leaderboard-item">
            <div class="rank-badge rank-3">
                <i class="fas fa-award award"></i>
            </div>
            <div class="student-info-leaderboard">
                <div class="student-details">2022001</div>
            </div>
            <div class="performance-metrics">
                <div class="metric-item score-bar-container">
                    <span class="metric-label">分数:</span>
                    <div class="score-bar">
                        <div class="score-fill" style="width: 3.5%;"></div>
                    </div>
                    <span class="score-value">238.1</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">时间:</span>
                    <span class="metric-value">4.200s</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">硬件:</span>
                    <span class="metric-value">AMD Ryzen 7 5800X + RTX 3070</span>
                </div>
            </div>
        </div>

        <!-- 第四名 -->
        <div class="leaderboard-item">
            <div class="rank-badge rank-other">
                <span>4</span>
            </div>
            <div class="student-info-leaderboard">
                <div class="student-details">2022005</div>
            </div>
            <div class="performance-metrics">
                <div class="metric-item score-bar-container">
                    <span class="metric-label">分数:</span>
                    <div class="score-bar">
                        <div class="score-fill" style="width: 2.2%;"></div>
                    </div>
                    <span class="score-value">147.1</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">时间:</span>
                    <span class="metric-value">6.800s</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">硬件:</span>
                    <span class="metric-value">Intel i5-10400F + GTX 1660</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
