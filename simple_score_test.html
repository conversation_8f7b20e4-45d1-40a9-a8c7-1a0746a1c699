<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单性能条测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .leaderboard-item {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
            min-height: 60px;
            border: 1px solid #ddd;
        }

        .rank-badge {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
            flex-shrink: 0;
            background: #28a745;
            color: white;
        }

        .student-info-leaderboard {
            min-width: 120px;
            flex-shrink: 0;
        }

        .student-details {
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }

        .performance-metrics {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 1;
        }

        .metric-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .score-bar-container {
            flex: 1;
            max-width: 400px;
            min-width: 200px;
        }

        .score-bar {
            height: 25px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #ccc;
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 12px;
            transition: width 0.8s ease;
            position: relative;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .score-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255,255,255,0.3), transparent);
            border-radius: 12px 12px 0 0;
        }

        .metric-label {
            font-weight: bold;
            min-width: 70px;
            font-size: 0.9rem;
            color: #555;
        }

        .score-value {
            min-width: 70px;
            font-size: 1.1rem;
            color: #28a745;
            font-weight: bold;
        }

        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>性能条显示测试</h1>
            
            <div class="debug-info">
                <strong>调试信息:</strong><br>
                排行榜数据: {{ leaderboard.length }} 条<br>
                最大分数: {{ maxScore }}<br>
                显示选项: {{ JSON.stringify(displayOptions) }}
            </div>

            <button @click="loadTestData" style="margin-bottom: 20px; padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px;">
                加载测试数据
            </button>

            <div v-if="leaderboard.length === 0">
                <p>点击上方按钮加载测试数据</p>
            </div>

            <div v-for="(item, index) in leaderboard" :key="index" class="leaderboard-item">
                <div class="rank-badge">{{ index + 1 }}</div>

                <div class="student-info-leaderboard">
                    <div class="student-details">{{ item.student_no }}</div>
                </div>

                <div class="performance-metrics">
                    <div v-if="displayOptions.showScore" class="metric-item score-bar-container">
                        <span class="metric-label">分数:</span>
                        <div class="score-bar">
                            <div class="score-fill"
                                 :style="{
                                     width: getScoreBarWidth(item.score) + '%'
                                 }">
                            </div>
                        </div>
                        <span class="score-value">{{ item.score.toFixed(1) }}</span>
                    </div>
                    
                    <div class="debug-info" style="margin: 0; padding: 5px; font-size: 0.7rem;">
                        宽度: {{ getScoreBarWidth(item.score) }}%
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;

        createApp({
            setup() {
                const leaderboard = ref([]);
                const displayOptions = ref({
                    showScore: true,
                    showRuntime: true,
                    showHardware: true
                });

                const maxScore = computed(() => {
                    const max = leaderboard.value.length > 0 ?
                        Math.max(...leaderboard.value.map(item => item.score)) : 0;
                    console.log('最大分数:', max);
                    return max;
                });

                const getScoreBarWidth = (score) => {
                    const currentMaxScore = leaderboard.value.length > 0 ?
                        Math.max(...leaderboard.value.map(item => item.score)) : 0;
                    if (currentMaxScore === 0) return 0;
                    const width = Math.min((score / currentMaxScore) * 100, 100);
                    console.log(`计算性能条宽度: 分数=${score}, 最大分数=${currentMaxScore}, 宽度=${width}%`);
                    return width;
                };

                const loadTestData = () => {
                    leaderboard.value = [
                        {
                            student_no: '2023171040',
                            score: 6833.17,
                            runtime: 0.146,
                            hardware: 'Intel i7-12700K + RTX 3080'
                        },
                        {
                            student_no: '2022003',
                            score: 400.0,
                            runtime: 2.5,
                            hardware: 'Intel i7-12700K + RTX 3080'
                        },
                        {
                            student_no: '2022001',
                            score: 238.1,
                            runtime: 4.2,
                            hardware: 'AMD Ryzen 7 5800X + RTX 3070'
                        }
                    ];
                    console.log('测试数据已加载:', leaderboard.value);
                };

                return {
                    leaderboard,
                    displayOptions,
                    maxScore,
                    getScoreBarWidth,
                    loadTestData,
                    JSON
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
