#!/usr/bin/env python3
"""
添加学生激活状态字段的数据库迁移脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from app.db.session import SessionLocal, engine
from app.models import Base

def add_student_active_status():
    """为学生表添加is_active字段"""
    print("开始添加学生激活状态字段...")
    
    db = SessionLocal()
    try:
        # 检查字段是否已存在（SQLite方式）
        try:
            result = db.execute(text("PRAGMA table_info(students)"))
            columns = [row[1] for row in result.fetchall()]  # row[1] 是列名

            if 'is_active' in columns:
                print("✅ is_active字段已存在，跳过添加")
                return
        except Exception as e:
            print(f"检查字段时出错: {e}")
            # 继续尝试添加字段
        
        # 添加is_active字段，默认值为True
        db.execute(text("""
            ALTER TABLE students 
            ADD COLUMN is_active BOOLEAN DEFAULT TRUE
        """))
        
        # 更新现有记录，设置为激活状态
        db.execute(text("""
            UPDATE students 
            SET is_active = TRUE 
            WHERE is_active IS NULL
        """))
        
        db.commit()
        print("✅ 成功添加is_active字段并设置默认值")
        
        # 验证添加结果
        result = db.execute(text("SELECT COUNT(*) as total, SUM(CASE WHEN is_active THEN 1 ELSE 0 END) as active FROM students"))
        row = result.fetchone()
        if row:
            print(f"📊 学生统计: 总数 {row.total}, 激活 {row.active}, 未激活 {row.total - row.active}")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 添加字段失败: {e}")
        raise
    finally:
        db.close()

def main():
    """主函数"""
    print("🔧 学生激活状态字段迁移工具")
    print("=" * 50)
    
    try:
        add_student_active_status()
        print("\n🎉 迁移完成！")
    except Exception as e:
        print(f"\n💥 迁移失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
