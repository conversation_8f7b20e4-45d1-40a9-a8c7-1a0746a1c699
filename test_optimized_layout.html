<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化后的性能排行榜布局测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: 
                radial-gradient(circle at 20% 80%, #2c3e50 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, #34495e 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, #1a252f 0%, transparent 50%),
                linear-gradient(135deg, #0f1419 0%, #1a252f 50%, #2c3e50 100%);
            color: #ecf0f1;
            overflow-x: hidden;
            min-height: 100vh;
            padding: 2rem;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(145deg, #2c3e50, #34495e);
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 
                0 8px 32px rgba(0,0,0,0.3),
                inset 0 1px 0 rgba(255,255,255,0.1);
            border: 1px solid #4a5568;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 20px rgba(52, 152, 219, 0.5);
            background: linear-gradient(45deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 控制面板样式 - 左下角 */
        .control-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: linear-gradient(145deg, #2c3e50, #34495e);
            color: #ecf0f1;
            border-radius: 12px;
            padding: 1rem;
            box-shadow: 
                0 8px 32px rgba(0,0,0,0.3),
                inset 0 1px 0 rgba(255,255,255,0.1);
            border: 1px solid #4a5568;
            z-index: 1000;
            min-width: 180px;
            backdrop-filter: blur(10px);
        }

        .control-panel h3 {
            margin-bottom: 1rem;
            font-size: 1rem;
            color: #3498db;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        /* 排行榜项目样式 */
        .leaderboard-item {
            background: linear-gradient(145deg, #2c3e50, #34495e);
            color: #ecf0f1;
            border-radius: 8px;
            padding: 0.8rem 1rem;
            margin-bottom: 0.5rem;
            box-shadow: 
                0 4px 16px rgba(0,0,0,0.2),
                inset 0 1px 0 rgba(255,255,255,0.1);
            border: 1px solid #4a5568;
            display: flex;
            align-items: center;
            gap: 1rem;
            min-height: 50px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .rank-badge {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1rem;
            flex-shrink: 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .rank-1 { background: linear-gradient(135deg, #ffd700, #ffed4e); color: #b8860b; }
        .rank-2 { background: linear-gradient(135deg, #c0c0c0, #e8e8e8); color: #666; }
        .rank-3 { background: linear-gradient(135deg, #cd7f32, #daa520); color: #8b4513; }
        .rank-other { background: linear-gradient(135deg, #6c757d, #495057); color: #fff; }

        /* 优化后的学生信息区域 */
        .student-info {
            min-width: 60px;
            max-width: 60px;
            flex-shrink: 0;
            overflow: hidden;
        }

        .student-name {
            font-size: 0.85rem;
            font-weight: 600;
            color: #ecf0f1;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
            margin-bottom: 0.1rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .hardware-info {
            display: flex;
            align-items: center;
            gap: 0.2rem;
            font-size: 0.65rem;
            color: #bdc3c7;
            opacity: 0.8;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .hardware-info i {
            color: #3498db;
            font-size: 0.6rem;
            flex-shrink: 0;
        }

        /* 优化后的性能区域 */
        .performance-section {
            flex: 2;
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .score-bar-wrapper {
            flex: 1;
            position: relative;
            min-width: 200px;
        }

        .score-bar-track {
            height: 20px;
            background: linear-gradient(90deg, #2c3e50, #34495e);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.3);
            border: 1px solid #4a5568;
        }

        .score-bar-fill {
            height: 100%;
            border-radius: 10px;
            position: relative;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            min-width: 2px;
            overflow: hidden;
        }

        .score-bar-shine {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255,255,255,0.3) 0%, transparent 100%);
            border-radius: 10px 10px 0 0;
        }

        .score-text {
            position: absolute;
            top: 50%;
            right: 8px;
            transform: translateY(-50%);
            color: #ecf0f1;
            font-size: 0.8rem;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0,0,0,0.8);
            font-family: 'Courier New', monospace;
        }

        .runtime-info {
            display: flex;
            align-items: center;
            gap: 0.2rem;
            color: #bdc3c7;
            font-size: 0.7rem;
            font-weight: 500;
            min-width: 50px;
            max-width: 50px;
            justify-content: flex-end;
            flex-shrink: 0;
        }

        .runtime-info i {
            color: #f39c12;
            font-size: 0.6rem;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 控制面板 - 左下角 -->
        <div class="control-panel">
            <h3><i class="fas fa-cog"></i> 显示控制</h3>
            <div class="control-option">
                <input type="checkbox" id="show-score" checked>
                <label for="show-score">显示分数条</label>
            </div>
            <div class="control-option">
                <input type="checkbox" id="show-runtime" checked>
                <label for="show-runtime">显示运行时间</label>
            </div>
            <div class="control-option">
                <input type="checkbox" id="show-hardware" checked>
                <label for="show-hardware">显示硬件信息</label>
            </div>
        </div>

        <div class="test-container">
            <div class="header">
                <h1>
                    <i class="fas fa-trophy"></i>
                    优化后的性能排行榜
                </h1>
                <p>紧凑布局 - 控制面板左下角 - 进度条更宽</p>
            </div>

            <div class="leaderboard-container">
                <div v-for="(item, index) in testData" :key="index" class="leaderboard-item">
                    <div class="rank-badge" :class="{
                        'rank-1': index === 0,
                        'rank-2': index === 1,
                        'rank-3': index === 2,
                        'rank-other': index > 2
                    }">
                        <i v-if="index === 0" class="fas fa-crown"></i>
                        <i v-else-if="index === 1" class="fas fa-medal"></i>
                        <i v-else-if="index === 2" class="fas fa-award"></i>
                        <span v-else>{{ index + 1 }}</span>
                    </div>

                    <div class="student-info">
                        <div class="student-name">{{ item.student_no }}</div>
                        <div class="hardware-info">
                            <i class="fas fa-microchip"></i>
                            {{ item.hardware }}
                        </div>
                    </div>
                    
                    <div class="performance-section">
                        <div class="score-bar-wrapper">
                            <div class="score-bar-track">
                                <div class="score-bar-fill" 
                                     :style="{ 
                                         width: getScoreBarWidth(item.score) + '%',
                                         background: getScoreGradient(item.score)
                                     }">
                                    <div class="score-bar-shine"></div>
                                </div>
                                <div class="score-text">{{ item.score.toFixed(0) }}分</div>
                            </div>
                        </div>
                        
                        <div class="runtime-info">
                            <i class="fas fa-clock"></i>
                            {{ item.runtime.toFixed(3) }}s
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const testData = ref([
                    { student_no: 'CS001', score: 1250, runtime: 0.123, hardware: 'RTX 4090' },
                    { student_no: 'CS002', score: 1180, runtime: 0.145, hardware: 'RTX 4080' },
                    { student_no: 'CS003', score: 1050, runtime: 0.167, hardware: 'RTX 4070' },
                    { student_no: 'CS004', score: 890, runtime: 0.189, hardware: 'RTX 3080' },
                    { student_no: 'CS005', score: 750, runtime: 0.234, hardware: 'RTX 3070' },
                    { student_no: 'CS006', score: 680, runtime: 0.267, hardware: 'GTX 1660' },
                    { student_no: 'CS007', score: 520, runtime: 0.345, hardware: 'GTX 1650' },
                    { student_no: 'CS008', score: 450, runtime: 0.456, hardware: 'GTX 1050' },
                    { student_no: 'CS009', score: 380, runtime: 0.567, hardware: 'Intel UHD' },
                    { student_no: 'CS010', score: 320, runtime: 0.678, hardware: 'Intel HD' },
                    { student_no: 'CS011', score: 280, runtime: 0.789, hardware: 'AMD Vega' },
                    { student_no: 'CS012', score: 220, runtime: 0.890, hardware: 'Intel Iris' },
                    { student_no: 'CS013', score: 180, runtime: 0.950, hardware: 'Intel UHD' },
                    { student_no: 'CS014', score: 150, runtime: 1.120, hardware: 'Intel HD' },
                    { student_no: 'CS015', score: 120, runtime: 1.340, hardware: 'AMD APU' }
                ]);

                const getScoreBarWidth = (score) => {
                    const maxScore = Math.max(...testData.value.map(item => item.score));
                    if (maxScore === 0) return 0;
                    const width = Math.min((score / maxScore) * 100, 100);
                    return Math.max(width, 1);
                };

                const getScoreGradient = (score) => {
                    if (score >= 1000) {
                        return 'linear-gradient(90deg, #ff6b35 0%, #f7931e 100%)';
                    } else if (score >= 500) {
                        return 'linear-gradient(90deg, #00d4aa 0%, #00a8cc 100%)';
                    } else if (score >= 300) {
                        return 'linear-gradient(90deg, #4ecdc4 0%, #44a08d 100%)';
                    } else if (score >= 100) {
                        return 'linear-gradient(90deg, #ffd93d 0%, #ff9a56 100%)';
                    } else {
                        return 'linear-gradient(90deg, #ff6b6b 0%, #ee5a52 100%)';
                    }
                };

                return {
                    testData,
                    getScoreBarWidth,
                    getScoreGradient
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
