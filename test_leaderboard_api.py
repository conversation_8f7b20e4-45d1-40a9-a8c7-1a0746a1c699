#!/usr/bin/env python3
"""
测试排行榜API
"""

import requests
import json

def test_leaderboard_api():
    """测试排行榜API"""
    print("=== 测试排行榜API ===")
    
    # 先登录获取token
    login_url = "http://localhost:30201/admin/login"
    login_data = {"username": "admin", "password": "admin123"}
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token = response.json()["access_token"]
            print("✅ 登录成功")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return False
    
    # 测试排行榜API
    leaderboard_url = "http://localhost:30201/admin/hardware-leaderboard"
    headers = {"Authorization": f"Bearer {token}"}
    params = {"question_id": 1, "limit": 10}
    
    try:
        response = requests.get(leaderboard_url, headers=headers, params=params)
        if response.status_code == 200:
            data = response.json()
            print("✅ 排行榜API调用成功")
            print(f"排行榜数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ 排行榜API调用失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 排行榜API异常: {e}")
        return False

if __name__ == "__main__":
    test_leaderboard_api()
