from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel

# Auth schemas
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"

# Class schemas
class ClassCreate(BaseModel):
    name: str
    code: Optional[str] = None

class ClassUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None

class ClassOut(BaseModel):
    id: int
    name: str
    code: Optional[str]
    is_active: bool
    created_at: datetime
    student_count: int = 0

    class Config:
        from_attributes = True

# Student schemas
class StudentCreate(BaseModel):
    student_no: str
    name: str
    class_id: int
    email: Optional[str] = None
    is_active: Optional[bool] = True

class StudentUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    is_active: Optional[bool] = None

class StudentOut(BaseModel):
    id: int
    student_no: str
    name: str
    class_id: int
    email: Optional[str]
    is_active: bool
    created_at: datetime
    class_name: Optional[str] = None
    submission_count: int = 0

    class Config:
        from_attributes = True

class StudentImport(BaseModel):
    student_no: str
    name: str
    email: Optional[str] = None

# Question schemas
class QuestionCreate(BaseModel):
    class_id: Optional[int] = None  # 修改为可选，创建时不需要指定班级
    title: str
    description: Optional[str] = None
    question_type: Optional[str] = "submit_result"  # submit_program, submit_data, submit_result
    experiment_type: Optional[str] = "general"  # 保留兼容性
    submission_type: Optional[str] = "image_data"
    order: Optional[int] = 0
    is_open: Optional[bool] = False  # 创建题目模板时默认不开放提交
    due_at: Optional[datetime] = None

class QuestionUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    question_type: Optional[str] = None
    experiment_type: Optional[str] = None
    submission_type: Optional[str] = None
    order: Optional[int] = None
    is_open: Optional[bool] = None
    is_published: Optional[bool] = None
    published_classes: Optional[str] = None
    due_at: Optional[datetime] = None

class QuestionOut(BaseModel):
    id: int
    class_id: int
    title: str
    description: Optional[str]
    experiment_type: str
    submission_type: str
    order: int
    is_open: bool
    due_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True

# Question Publication schemas
class QuestionPublicationCreate(BaseModel):
    question_id: int
    class_ids: List[int]  # 发布到的班级ID列表

class QuestionPublicationOut(BaseModel):
    id: int
    question_id: int
    class_id: int
    is_active: bool
    published_at: datetime
    created_at: datetime

    class Config:
        from_attributes = True

# Submission schemas
class SubmissionOut(BaseModel):
    id: int
    class_id: int
    question_id: int
    student_no: str
    student_name: Optional[str]
    note: Optional[str]
    numeric: Optional[List[float]] = None
    image_url: Optional[str] = None
    thumb_url: Optional[str] = None
    mime: Optional[str]
    size: Optional[int]
    score: Optional[float]
    comment: Optional[str]
    visible: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class SubmissionUpdateByAdmin(BaseModel):
    score: Optional[float] = None
    comment: Optional[str] = None
    visible: Optional[bool] = None

class PublicSubmissionOut(BaseModel):
    submission_id: int
    student_no_masked: str
    image_url: Optional[str]
    thumb_url: Optional[str]
    note: Optional[str]
    created_at: datetime
    score: Optional[float] = None
    runtime: Optional[float] = None
    numeric: Optional[List[float]] = None

# API response schemas
class SubmissionCreateResponse(BaseModel):
    ok: bool = True
    submission_id: int
    image: dict  # {"saved": bool}
    numeric_count: int
