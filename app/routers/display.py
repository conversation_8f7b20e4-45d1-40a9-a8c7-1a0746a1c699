import json
from typing import Optional, List
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models import Submission, Question, Class
from app.services.files import get_image_url

router = APIRouter(prefix="/display", tags=["display"])

@router.get("/submissions")
def get_display_submissions(
    class_id: List[int] = Query(..., description="班级ID列表"),
    question_id: int = Query(..., description="题目ID"),
    db: Session = Depends(get_db)
):
    """获取课堂展示的提交数据"""
    from typing import List

    # 验证题目是否存在
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    # 验证班级是否存在
    class_names = []
    for cid in class_id:
        class_obj = db.query(Class).filter(Class.id == cid).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail=f"班级ID {cid} 不存在")
        class_names.append(class_obj.name)

    # 获取多个班级该题目的所有可见提交
    submissions = db.query(Submission).filter(
        Submission.class_id.in_(class_id),
        Submission.question_id == question_id,
        Submission.visible == True  # 只显示可见的提交
    ).order_by(Submission.created_at.desc()).all()
    
    # 构建返回数据
    submission_list = []
    for sub in submissions:
        # 解析数字数组
        numeric_data = None
        if sub.numeric_json:
            try:
                numeric_data = json.loads(sub.numeric_json)
            except:
                pass
        
        submission_data = {
            "id": sub.id,
            "student_no": sub.student_no,
            "student_name": sub.student_name or f"学生_{sub.student_no}",
            "note": sub.note,
            "numeric": numeric_data,
            "image_url": get_image_url(sub.image_path),
            "thumb_url": get_image_url(sub.thumb_path),
            "text_data": sub.text_content,  # 课堂展示页面期望的字段名
            "score": sub.score,
            "runtime": sub.runtime,
            "created_at": sub.created_at,
            "updated_at": sub.updated_at
        }
        submission_list.append(submission_data)
    
    return {
        "submissions": submission_list,
        "question_title": question.title,
        "class_name": " + ".join(class_names) if len(class_names) > 1 else class_names[0],
        "class_names": class_names,
        "total_count": len(submission_list)
    }

@router.get("/leaderboard")
def get_display_leaderboard(
    question_id: int = Query(..., description="题目ID"),
    class_id: Optional[int] = Query(None, description="班级ID（可选）"),
    limit: int = Query(50, description="返回数量限制"),
    db: Session = Depends(get_db)
):
    """获取课堂展示的性能排行榜数据（公开接口）"""

    # 验证题目是否存在
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    # 构建查询
    query = db.query(Submission).filter(
        Submission.question_id == question_id,
        Submission.runtime.isnot(None),  # 只包含有运行时间的提交
        Submission.runtime > 0,  # 运行时间必须大于0
        Submission.visible == True  # 只显示可见的提交
    )

    # 如果指定了班级ID，则过滤班级
    if class_id:
        query = query.filter(Submission.class_id == class_id)

    # 按运行时间升序排列（运行时间越短排名越高）
    submissions = query.order_by(Submission.runtime.asc()).limit(limit).all()

    # 构建排行榜数据
    leaderboard = []
    for rank, submission in enumerate(submissions, 1):
        # 计算性能分数：1000 / runtime
        score = 1000.0 / submission.runtime if submission.runtime > 0 else 0

        # 获取班级名称
        class_name = "未知班级"
        if submission.class_id:
            class_obj = db.query(Class).filter(Class.id == submission.class_id).first()
            if class_obj:
                class_name = class_obj.name

        leaderboard_item = {
            "rank": rank,
            "submission_id": submission.id,
            "student_no": submission.student_no,
            "student_name": submission.student_name or f"学生_{submission.student_no}",
            "class_id": submission.class_id,
            "class_name": class_name,
            "question_id": submission.question_id,
            "runtime": submission.runtime,
            "hardware": submission.hardware,
            "score": round(score, 2),
            "submitted_at": submission.created_at
        }
        leaderboard.append(leaderboard_item)

    return {
        "leaderboard": leaderboard,
        "total_count": len(leaderboard),
        "question_id": question_id,
        "class_id": class_id,
        "question_title": question.title
    }
