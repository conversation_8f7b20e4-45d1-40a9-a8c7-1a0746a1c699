import csv
import io
import json
from datetime import timedelta, datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Response, Query
from sqlalchemy.orm import Session

from app.core.security import get_current_admin, create_access_token, verify_password
from app.core.config import ACCESS_TOKEN_EXPIRE_MINUTES
from app.db.session import get_db
from app.models import AdminUser, Class, Question, Submission, Student, QuestionPublication
from app.schemas import (
    LoginRequest, LoginResponse, ClassCreate, ClassUpdate, ClassOut,
    QuestionCreate, QuestionUpdate, QuestionOut, SubmissionOut, SubmissionUpdateByAdmin,
    StudentCreate, StudentUpdate, StudentOut, StudentImport, QuestionPublicationCreate, QuestionPublicationOut
)
from app.services.files import get_image_url

router = APIRouter(prefix="/admin", tags=["admin"])

@router.post("/login", response_model=LoginResponse)
def login(request: LoginRequest, db: Session = Depends(get_db)):
    """管理员登录"""
    admin = db.query(AdminUser).filter(AdminUser.username == request.username).first()
    if not admin or not verify_password(request.password, admin.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": admin.username}, expires_delta=access_token_expires
    )
    
    return LoginResponse(access_token=access_token)

@router.get("/me")
def get_current_user(current_admin: AdminUser = Depends(get_current_admin)):
    """获取当前管理员信息"""
    return {"username": current_admin.username, "id": current_admin.id}

@router.get("/classes", response_model=List[ClassOut])
def get_classes(
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取所有班级"""
    from sqlalchemy.orm import joinedload

    classes = db.query(Class).options(joinedload(Class.students)).order_by(Class.created_at.desc()).all()

    # 构建包含学生人数的班级信息
    result = []
    for class_obj in classes:
        class_dict = {
            "id": class_obj.id,
            "name": class_obj.name,
            "code": class_obj.code,
            "is_active": class_obj.is_active,
            "created_at": class_obj.created_at,
            "student_count": len(class_obj.students)
        }
        result.append(class_dict)

    return result

@router.post("/classes", response_model=ClassOut)
def create_class(
    request: ClassCreate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """创建班级"""
    # 检查班级代码是否已存在
    if request.code:
        existing = db.query(Class).filter(Class.code == request.code).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"班级代码 '{request.code}' 已存在"
            )
    
    new_class = Class(name=request.name, code=request.code)
    db.add(new_class)
    db.commit()
    db.refresh(new_class)
    
    return new_class

@router.patch("/classes/{class_id}", response_model=ClassOut)
def update_class(
    class_id: int,
    request: ClassUpdate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """更新班级"""
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")
    
    # 检查班级代码冲突
    if request.code and request.code != class_obj.code:
        existing = db.query(Class).filter(Class.code == request.code).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"班级代码 '{request.code}' 已存在"
            )
    
    if request.name is not None:
        class_obj.name = request.name
    if request.code is not None:
        class_obj.code = request.code
    
    db.commit()
    db.refresh(class_obj)
    return class_obj

@router.patch("/classes/{class_id}/activate")
def toggle_class_activation(
    class_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """切换班级激活状态（允许多个班级同时激活）"""
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    # 切换激活状态
    class_obj.is_active = not class_obj.is_active
    db.commit()

    status_text = "激活" if class_obj.is_active else "取消激活"
    return {"message": f"班级 '{class_obj.name}' 已{status_text}", "is_active": class_obj.is_active}

@router.delete("/classes/{class_id}")
def delete_class(
    class_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """删除班级"""
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    # 检查是否有关联的学生
    student_count = db.query(Student).filter(Student.class_id == class_id).count()
    if student_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除班级，还有 {student_count} 个学生关联到此班级"
        )

    # 检查是否有关联的题目
    question_count = db.query(Question).filter(Question.class_id == class_id).count()
    if question_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除班级，还有 {question_count} 个题目关联到此班级"
        )

    db.delete(class_obj)
    db.commit()

    return {"message": f"班级 '{class_obj.name}' 删除成功"}

# 任务管理 (原题目管理)
@router.get("/tasks", response_model=List[QuestionOut])
def get_tasks(
    class_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取任务列表"""
    query = db.query(Question)
    if class_id:
        query = query.filter(Question.class_id == class_id)

    tasks = query.order_by(Question.order, Question.created_at).all()
    return tasks

@router.get("/classes/{class_id}/tasks", response_model=List[QuestionOut])
def get_class_tasks(
    class_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取班级的所有任务"""
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    tasks = db.query(Question).filter(
        Question.class_id == class_id
    ).order_by(Question.order, Question.created_at).all()

    return tasks

@router.post("/tasks", response_model=QuestionOut)
def create_task(
    request: QuestionCreate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """创建任务"""
    # 验证班级存在
    class_obj = db.query(Class).filter(Class.id == request.class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    new_task = Question(
        class_id=request.class_id,
        title=request.title,
        description=request.description,
        experiment_type=request.experiment_type or "general",
        submission_type=request.submission_type or "image_data",
        order=request.order or 0,
        is_open=request.is_open if request.is_open is not None else True,
        due_at=request.due_at
    )

    db.add(new_task)
    db.commit()
    db.refresh(new_task)

    return new_task

@router.patch("/tasks/{task_id}", response_model=QuestionOut)
def update_task(
    task_id: int,
    request: QuestionUpdate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """更新任务"""
    task = db.query(Question).filter(Question.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    if request.title is not None:
        task.title = request.title
    if request.description is not None:
        task.description = request.description
    if request.experiment_type is not None:
        task.experiment_type = request.experiment_type
    if request.submission_type is not None:
        task.submission_type = request.submission_type
    if request.order is not None:
        task.order = request.order
    if request.is_open is not None:
        task.is_open = request.is_open
    if request.due_at is not None:
        task.due_at = request.due_at

    db.commit()
    db.refresh(task)
    return task

@router.delete("/tasks/{task_id}")
def delete_task(
    task_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """删除任务"""
    task = db.query(Question).filter(Question.id == task_id).first()
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    # 检查是否有关联的提交
    submission_count = db.query(Submission).filter(Submission.question_id == task_id).count()
    if submission_count > 0:
        raise HTTPException(
            status_code=400,
            detail=f"无法删除任务，还有 {submission_count} 个提交关联到此任务"
        )

    db.delete(task)
    db.commit()

    return {"message": f"任务 '{task.title}' 删除成功"}

# 题目管理API (新增)
@router.get("/questions")
async def get_questions(class_id: Optional[int] = None, db: Session = Depends(get_db)):
    """获取题目列表"""
    query = db.query(Question)
    if class_id:
        query = query.filter(Question.class_id == class_id)

    questions = query.all()

    # 添加班级名称和提交数量
    result = []
    for question in questions:
        class_name = question.class_.name if question.class_ else None
        submission_count = db.query(Submission).filter(Submission.question_id == question.id).count()

        result.append({
            "id": question.id,
            "title": question.title,
            "description": question.description,
            "question_type": getattr(question, 'question_type', 'submit_result'),
            "submission_type": question.submission_type,
            "is_open": question.is_open,
            "is_published": getattr(question, 'is_published', False),
            "published_classes": getattr(question, 'published_classes', None),
            "class_id": question.class_id,
            "class_name": class_name,
            "submission_count": submission_count,
            "created_at": question.created_at
        })

    return result

@router.post("/questions")
async def create_question(question: QuestionCreate, db: Session = Depends(get_db)):
    """创建题目（不需要指定班级，发布时再选择班级）"""
    # 如果指定了班级，验证班级是否存在
    if question.class_id:
        class_obj = db.query(Class).filter(Class.id == question.class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail="班级不存在")

    db_question = Question(
        class_id=question.class_id,  # 可以为None
        title=question.title,
        description=question.description,
        submission_type=question.submission_type,
        is_open=False  # 创建题目模板时默认不开放提交，需要发布后才开放
    )

    # 如果模型支持新字段，则设置
    if hasattr(Question, 'question_type'):
        db_question.question_type = getattr(question, 'question_type', 'submit_result')

    db.add(db_question)
    db.commit()
    db.refresh(db_question)

    return {"message": "题目创建成功", "question_id": db_question.id}

@router.get("/questions/{question_id}")
async def get_question(question_id: int, db: Session = Depends(get_db)):
    """获取单个题目详情"""
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    return {
        "id": question.id,
        "title": question.title,
        "description": question.description,
        "question_type": getattr(question, 'question_type', 'submit_result'),
        "submission_type": question.submission_type,
        "is_open": question.is_open,
        "is_published": getattr(question, 'is_published', False),
        "published_classes": getattr(question, 'published_classes', None),
        "class_id": question.class_id,
        "created_at": question.created_at
    }

@router.patch("/questions/{question_id}")
async def update_question(question_id: int, update_data: dict, db: Session = Depends(get_db)):
    """更新题目"""
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    for key, value in update_data.items():
        if hasattr(question, key):
            setattr(question, key, value)

    db.commit()
    return {"message": "题目更新成功"}

@router.post("/questions/{question_id}/publish")
async def publish_question(question_id: int, publish_data: dict, db: Session = Depends(get_db)):
    """发布题目到指定班级"""
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    class_ids = publish_data.get("class_ids", [])
    if not class_ids:
        raise HTTPException(status_code=400, detail="请选择至少一个班级")

    # 验证班级是否存在
    for class_id in class_ids:
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail=f"班级ID {class_id} 不存在")

    # 创建发布记录
    published_count = 0
    for class_id in class_ids:
        # 检查是否已经发布到该班级
        existing = db.query(QuestionPublication).filter(
            QuestionPublication.question_id == question_id,
            QuestionPublication.class_id == class_id
        ).first()

        if existing:
            # 如果已存在，更新为激活状态
            existing.is_active = True
            existing.updated_at = datetime.utcnow()
        else:
            # 创建新的发布记录
            publication = QuestionPublication(
                question_id=question_id,
                class_id=class_id,
                is_active=True
            )
            db.add(publication)
            published_count += 1

    # 更新题目的发布状态（保持兼容性）
    question.is_published = True
    question.published_classes = json.dumps(class_ids)

    db.commit()
    return {"message": f"题目发布成功，共发布到 {len(class_ids)} 个班级", "published_count": published_count}

@router.get("/questions/{question_id}/publications")
async def get_question_publications(question_id: int, db: Session = Depends(get_db)):
    """获取题目的发布状态"""
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    publications = db.query(QuestionPublication).filter(
        QuestionPublication.question_id == question_id
    ).all()

    result = []
    for pub in publications:
        class_obj = db.query(Class).filter(Class.id == pub.class_id).first()
        result.append({
            "id": pub.id,
            "class_id": pub.class_id,
            "class_name": class_obj.name if class_obj else "未知班级",
            "is_active": pub.is_active,
            "published_at": pub.published_at,
            "created_at": pub.created_at
        })

    return result

@router.patch("/questions/{question_id}/publications/{publication_id}")
async def toggle_question_publication(
    question_id: int,
    publication_id: int,
    db: Session = Depends(get_db)
):
    """切换题目发布状态（激活/停用）"""
    publication = db.query(QuestionPublication).filter(
        QuestionPublication.id == publication_id,
        QuestionPublication.question_id == question_id
    ).first()

    if not publication:
        raise HTTPException(status_code=404, detail="发布记录不存在")

    publication.is_active = not publication.is_active
    publication.updated_at = datetime.utcnow()
    db.commit()

    status_text = "激活" if publication.is_active else "停用"
    return {"message": f"题目发布状态已{status_text}", "is_active": publication.is_active}

@router.delete("/questions/{question_id}")
async def delete_question(question_id: int, db: Session = Depends(get_db)):
    """删除题目"""
    question = db.query(Question).filter(Question.id == question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    # 删除题目（会级联删除相关的提交记录）
    db.delete(question)
    db.commit()
    return {"message": "题目删除成功"}

@router.get("/question-publications")
def get_question_publications(
    class_id: Optional[int] = None,
    question_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取题目发布实例列表"""
    try:
        from sqlalchemy.orm import joinedload

        query = db.query(QuestionPublication).options(
            joinedload(QuestionPublication.question),
            joinedload(QuestionPublication.class_)
        )

        if class_id:
            query = query.filter(QuestionPublication.class_id == class_id)
        if question_id:
            query = query.filter(QuestionPublication.question_id == question_id)

        publications = query.order_by(QuestionPublication.published_at.desc()).all()

        # 构建返回数据
        result = []
        for pub in publications:
            try:
                # 检查关联数据是否存在
                if not pub.question or not pub.class_:
                    continue

                # 计算提交数量
                submission_count = db.query(Submission).filter(
                    Submission.question_id == pub.question_id,
                    Submission.class_id == pub.class_id
                ).count()

                pub_dict = {
                    "id": pub.id,
                    "question_id": pub.question_id,
                    "question_title": pub.question.title,
                    "class_id": pub.class_id,
                    "class_name": pub.class_.name,
                    "is_active": pub.is_active,
                    "published_at": pub.published_at,
                    "submission_count": submission_count
                }
                result.append(pub_dict)
            except Exception as e:
                # 跳过有问题的记录，继续处理其他记录
                print(f"处理发布记录 {pub.id} 时出错: {e}")
                continue

        return result
    except Exception as e:
        print(f"获取题目发布列表时出错: {e}")
        raise HTTPException(status_code=500, detail="获取题目发布列表失败")

@router.post("/question-publications")
def create_question_publication(
    request: QuestionPublicationCreate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """创建题目发布实例"""
    # 验证题目是否存在
    question = db.query(Question).filter(Question.id == request.question_id).first()
    if not question:
        raise HTTPException(status_code=404, detail="题目不存在")

    # 验证班级是否存在
    for class_id in request.class_ids:
        class_obj = db.query(Class).filter(Class.id == class_id).first()
        if not class_obj:
            raise HTTPException(status_code=404, detail=f"班级ID {class_id} 不存在")

    # 创建发布记录
    created_count = 0
    for class_id in request.class_ids:
        # 检查是否已经发布到该班级
        existing = db.query(QuestionPublication).filter(
            QuestionPublication.question_id == request.question_id,
            QuestionPublication.class_id == class_id
        ).first()

        if existing:
            # 如果已存在，更新为激活状态
            existing.is_active = True
            existing.updated_at = datetime.utcnow()
        else:
            # 创建新的发布记录
            publication = QuestionPublication(
                question_id=request.question_id,
                class_id=class_id,
                is_active=True
            )
            db.add(publication)
            created_count += 1

    # 发布题目时自动开放提交
    question.is_open = True
    question.is_published = True
    question.published_classes = json.dumps(request.class_ids)

    db.commit()
    return {"message": f"题目发布成功，共发布到 {len(request.class_ids)} 个班级", "created_count": created_count}

@router.patch("/question-publications/{publication_id}/toggle")
def toggle_question_publication(
    publication_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """切换题目发布实例的激活状态"""
    publication = db.query(QuestionPublication).filter(QuestionPublication.id == publication_id).first()
    if not publication:
        raise HTTPException(status_code=404, detail="题目发布实例不存在")

    publication.is_active = not publication.is_active
    publication.updated_at = datetime.utcnow()
    db.commit()

    status_text = "激活" if publication.is_active else "关闭"
    return {"message": f"题目实例已{status_text}"}

@router.delete("/question-publications/{publication_id}")
def delete_question_publication(
    publication_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """删除题目发布实例"""
    publication = db.query(QuestionPublication).filter(QuestionPublication.id == publication_id).first()
    if not publication:
        raise HTTPException(status_code=404, detail="题目发布实例不存在")

    db.delete(publication)
    db.commit()

    return {"message": "题目实例删除成功"}

@router.get("/submissions", response_model=List[SubmissionOut])
def get_submissions(
    class_id: Optional[List[int]] = Query(None),
    question_id: Optional[int] = None,
    student_no: Optional[str] = None,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取提交列表"""
    from sqlalchemy.orm import joinedload

    query = db.query(Submission).options(
        joinedload(Submission.student),
        joinedload(Submission.question),
        joinedload(Submission.class_)
    )

    if class_id:
        query = query.filter(Submission.class_id.in_(class_id))
    if question_id:
        query = query.filter(Submission.question_id == question_id)
    if student_no:
        query = query.filter(Submission.student_no.contains(student_no))

    submissions = query.order_by(Submission.updated_at.desc()).all()

    # 转换为响应格式
    result = []
    for sub in submissions:
        numeric_data = None
        if sub.numeric_json:
            try:
                numeric_data = json.loads(sub.numeric_json)
            except:
                pass

        # 构建提交数据，包含题目标题
        submission_data = {
            "id": sub.id,
            "class_id": sub.class_id,
            "question_id": sub.question_id,
            "student_no": sub.student_no,
            "student_name": sub.student_name,
            "question_title": sub.question.title if sub.question else "未知题目",
            "note": sub.note,
            "numeric": numeric_data,
            "image_url": get_image_url(sub.image_path),
            "thumb_url": get_image_url(sub.thumb_path),
            "mime": sub.mime,
            "size": sub.size,
            "score": sub.score,
            "comment": sub.comment,
            "visible": sub.visible,
            "created_at": sub.created_at,
            "updated_at": sub.updated_at
        }
        result.append(submission_data)

    return result

@router.get("/submissions/{submission_id}", response_model=SubmissionOut)
def get_submission(
    submission_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取单个提交详情"""
    submission = db.query(Submission).filter(Submission.id == submission_id).first()
    if not submission:
        raise HTTPException(status_code=404, detail="提交不存在")

    numeric_data = None
    if submission.numeric_json:
        try:
            numeric_data = json.loads(submission.numeric_json)
        except:
            pass

    return SubmissionOut(
        id=submission.id,
        class_id=submission.class_id,
        question_id=submission.question_id,
        student_no=submission.student_no,
        student_name=submission.student_name,
        note=submission.note,
        numeric=numeric_data,
        image_url=get_image_url(submission.image_path),
        thumb_url=get_image_url(submission.thumb_path),
        mime=submission.mime,
        size=submission.size,
        score=submission.score,
        comment=submission.comment,
        visible=submission.visible,
        created_at=submission.created_at,
        updated_at=submission.updated_at
    )

@router.patch("/submissions/{submission_id}", response_model=SubmissionOut)
def update_submission(
    submission_id: int,
    request: SubmissionUpdateByAdmin,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """更新提交（管理员）"""
    submission = db.query(Submission).filter(Submission.id == submission_id).first()
    if not submission:
        raise HTTPException(status_code=404, detail="提交不存在")

    if request.score is not None:
        submission.score = request.score
    if request.comment is not None:
        submission.comment = request.comment
    if request.visible is not None:
        submission.visible = request.visible

    db.commit()
    db.refresh(submission)

    # 返回更新后的数据
    numeric_data = None
    if submission.numeric_json:
        try:
            numeric_data = json.loads(submission.numeric_json)
        except:
            pass

    return SubmissionOut(
        id=submission.id,
        class_id=submission.class_id,
        question_id=submission.question_id,
        student_no=submission.student_no,
        student_name=submission.student_name,
        note=submission.note,
        numeric=numeric_data,
        image_url=get_image_url(submission.image_path),
        thumb_url=get_image_url(submission.thumb_path),
        mime=submission.mime,
        size=submission.size,
        score=submission.score,
        comment=submission.comment,
        visible=submission.visible,
        created_at=submission.created_at,
        updated_at=submission.updated_at
    )

@router.delete("/submissions/{submission_id}")
def delete_submission(
    submission_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """删除提交记录"""
    submission = db.query(Submission).filter(Submission.id == submission_id).first()
    if not submission:
        raise HTTPException(status_code=404, detail="提交记录不存在")

    # 删除相关文件
    if submission.image_path or submission.thumb_path:
        from app.services.files import delete_old_files
        delete_old_files(submission.image_path, submission.thumb_path)

    db.delete(submission)
    db.commit()

    return {"message": "提交记录删除成功"}

@router.get("/export/csv")
def export_csv(
    class_id: Optional[int] = None,
    question_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """导出 CSV"""
    query = db.query(Submission)

    if class_id:
        query = query.filter(Submission.class_id == class_id)
    if question_id:
        query = query.filter(Submission.question_id == question_id)

    submissions = query.order_by(Submission.student_no, Submission.question_id).all()

    # 生成 CSV
    output = io.StringIO()
    writer = csv.writer(output)

    # 写入表头
    writer.writerow([
        "学号", "姓名", "题目ID", "得分", "是否可见", "提交时间",
        "数值数组", "图片路径", "备注"
    ])

    # 写入数据
    for sub in submissions:
        writer.writerow([
            sub.student_no,
            sub.student_name or "",
            sub.question_id,
            sub.score or "",
            "是" if sub.visible else "否",
            sub.created_at.strftime("%Y-%m-%d %H:%M:%S"),
            sub.numeric_json or "",
            sub.image_path or "",
            sub.note or ""
        ])

    csv_content = output.getvalue()
    output.close()

    return Response(
        content=csv_content,
        media_type="text/csv",
        headers={"Content-Disposition": "attachment; filename=submissions.csv"}
    )

# 学生管理路由
@router.get("/students", response_model=List[StudentOut])
def get_students(
    class_id: Optional[int] = None,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取学生列表"""
    from sqlalchemy.orm import joinedload

    query = db.query(Student).options(joinedload(Student.class_), joinedload(Student.submissions))
    if class_id:
        query = query.filter(Student.class_id == class_id)

    students = query.all()

    # 添加班级名称和提交数量
    result = []
    for student in students:
        student_dict = {
            "id": student.id,
            "student_no": student.student_no,
            "name": student.name,
            "class_id": student.class_id,
            "email": student.email,
            "is_active": student.is_active,
            "created_at": student.created_at,
            "class_name": student.class_.name if student.class_ else None,
            "submission_count": len(student.submissions)
        }
        result.append(student_dict)

    return result

@router.post("/students", response_model=StudentOut)
def create_student(
    student: StudentCreate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """创建学生"""
    # 检查班级是否存在
    class_obj = db.query(Class).filter(Class.id == student.class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    # 检查学号是否在班级内重复
    existing = db.query(Student).filter(
        Student.class_id == student.class_id,
        Student.student_no == student.student_no
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="该班级内学号已存在")

    db_student = Student(**student.dict())
    db.add(db_student)
    db.commit()
    db.refresh(db_student)

    return db_student

@router.post("/students/import")
def import_students(
    data: dict,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """批量导入学生"""
    class_id = data.get("class_id")
    students_data = data.get("students", [])

    if not class_id or not students_data:
        raise HTTPException(status_code=400, detail="缺少必要参数")

    # 检查班级是否存在
    class_obj = db.query(Class).filter(Class.id == class_id).first()
    if not class_obj:
        raise HTTPException(status_code=404, detail="班级不存在")

    success_count = 0
    errors = []

    for student_data in students_data:
        try:
            # 检查学号是否重复
            existing = db.query(Student).filter(
                Student.class_id == class_id,
                Student.student_no == student_data["student_no"]
            ).first()

            if existing:
                errors.append(f"学号 {student_data['student_no']} 已存在")
                continue

            db_student = Student(
                class_id=class_id,
                student_no=student_data["student_no"],
                name=student_data["name"],
                email=student_data.get("email")
            )
            db.add(db_student)
            success_count += 1

        except Exception as e:
            errors.append(f"学号 {student_data.get('student_no', '未知')}: {str(e)}")

    db.commit()

    return {
        "success_count": success_count,
        "errors": errors,
        "message": f"成功导入 {success_count} 个学生"
    }

@router.patch("/students/{student_id}", response_model=StudentOut)
def update_student(
    student_id: int,
    student: StudentUpdate,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """更新学生信息"""
    db_student = db.query(Student).filter(Student.id == student_id).first()
    if not db_student:
        raise HTTPException(status_code=404, detail="学生不存在")

    update_data = student.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_student, field, value)

    db.commit()
    db.refresh(db_student)

    return db_student

@router.delete("/students/{student_id}")
def delete_student(
    student_id: int,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """删除学生"""
    db_student = db.query(Student).filter(Student.id == student_id).first()
    if not db_student:
        raise HTTPException(status_code=404, detail="学生不存在")

    db.delete(db_student)
    db.commit()

    return {"message": "学生删除成功"}

@router.get("/hardware-leaderboard")
def get_hardware_leaderboard(
    question_id: Optional[int] = None,
    class_id: Optional[int] = None,
    limit: int = 50,
    db: Session = Depends(get_db),
    current_admin: AdminUser = Depends(get_current_admin)
):
    """获取硬件性能排行榜"""
    try:
        # 构建查询
        query = db.query(Submission).filter(
            Submission.runtime.isnot(None),
            Submission.runtime > 0,
            Submission.hardware.isnot(None)
        )

        if question_id:
            query = query.filter(Submission.question_id == question_id)
        if class_id:
            query = query.filter(Submission.class_id == class_id)

        # 获取提交记录
        submissions = query.order_by(Submission.runtime.asc()).limit(limit).all()

        # 构建排行榜数据
        leaderboard = []
        for i, submission in enumerate(submissions):
            # 计算性能分数：1000 / runtime
            score = 1000.0 / submission.runtime if submission.runtime > 0 else 0

            leaderboard.append({
                "rank": i + 1,
                "student_no": submission.student_no,
                "student_name": submission.student_name,
                "hardware": submission.hardware,
                "runtime": submission.runtime,
                "score": round(score, 2),
                "question_id": submission.question_id,
                "class_id": submission.class_id,
                "submitted_at": submission.created_at
            })

        return {
            "leaderboard": leaderboard,
            "total_count": len(leaderboard),
            "question_id": question_id,
            "class_id": class_id
        }

    except Exception as e:
        print(f"获取硬件排行榜时出错: {e}")
        raise HTTPException(status_code=500, detail="获取硬件排行榜失败")
