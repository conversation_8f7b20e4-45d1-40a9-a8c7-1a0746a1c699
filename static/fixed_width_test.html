<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>固定宽度性能条测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #ddd;
        }

        .label {
            min-width: 100px;
            font-weight: bold;
        }

        .score-bar-container {
            flex: 1;
            max-width: 400px;
            min-width: 200px;
        }

        .score-bar {
            height: 25px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #ccc;
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 12px;
            transition: width 0.8s ease;
            position: relative;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .score-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255,255,255,0.3), transparent);
            border-radius: 12px 12px 0 0;
        }

        .score-value {
            min-width: 80px;
            font-size: 1.1rem;
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>固定宽度性能条测试</h1>
        <p>这个测试使用固定的CSS宽度值来验证性能条是否能正常显示。</p>
        
        <div class="test-item">
            <span class="label">100%宽度:</span>
            <div class="score-bar-container">
                <div class="score-bar">
                    <div class="score-fill" style="width: 100%;"></div>
                </div>
            </div>
            <span class="score-value">6833.2</span>
        </div>

        <div class="test-item">
            <span class="label">75%宽度:</span>
            <div class="score-bar-container">
                <div class="score-bar">
                    <div class="score-fill" style="width: 75%;"></div>
                </div>
            </div>
            <span class="score-value">5124.9</span>
        </div>

        <div class="test-item">
            <span class="label">50%宽度:</span>
            <div class="score-bar-container">
                <div class="score-bar">
                    <div class="score-fill" style="width: 50%;"></div>
                </div>
            </div>
            <span class="score-value">3416.6</span>
        </div>

        <div class="test-item">
            <span class="label">25%宽度:</span>
            <div class="score-bar-container">
                <div class="score-bar">
                    <div class="score-fill" style="width: 25%;"></div>
                </div>
            </div>
            <span class="score-value">1708.3</span>
        </div>

        <div class="test-item">
            <span class="label">6%宽度:</span>
            <div class="score-bar-container">
                <div class="score-bar">
                    <div class="score-fill" style="width: 6%;"></div>
                </div>
            </div>
            <span class="score-value">400.0</span>
        </div>

        <div class="test-item">
            <span class="label">3%宽度:</span>
            <div class="score-bar-container">
                <div class="score-bar">
                    <div class="score-fill" style="width: 3%;"></div>
                </div>
            </div>
            <span class="score-value">238.1</span>
        </div>

        <div class="test-item">
            <span class="label">1%宽度:</span>
            <div class="score-bar-container">
                <div class="score-bar">
                    <div class="score-fill" style="width: 1%;"></div>
                </div>
            </div>
            <span class="score-value">68.3</span>
        </div>

        <p><strong>如果您能看到上面的绿色性能条，说明CSS样式工作正常。</strong></p>
        <p>如果看不到绿色条，可能是CSS样式问题。</p>
    </div>
</body>
</html>
