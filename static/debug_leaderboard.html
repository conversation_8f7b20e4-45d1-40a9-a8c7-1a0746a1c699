<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>排行榜调试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }

        .debug-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .leaderboard-item {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 15px;
            padding: 1rem 1.5rem;
            margin-bottom: 0.8rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
            min-height: 60px;
            border: 2px solid #ddd;
        }

        .rank-badge {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
            flex-shrink: 0;
            background: #28a745;
            color: white;
        }

        .student-info-leaderboard {
            min-width: 120px;
            flex-shrink: 0;
        }

        .student-details {
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }

        .performance-metrics {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 1;
        }

        .metric-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .score-bar-container {
            flex: 1;
            max-width: 400px;
            min-width: 200px;
        }

        .score-bar {
            height: 25px;
            background: #e9ecef;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #ccc;
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            border-radius: 12px;
            transition: width 0.8s ease;
            position: relative;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }

        .metric-label {
            font-weight: bold;
            min-width: 70px;
            font-size: 0.9rem;
            color: #555;
        }

        .metric-value {
            min-width: 60px;
            font-size: 0.9rem;
            color: #333;
            font-weight: 600;
        }

        .score-value {
            min-width: 70px;
            font-size: 1.1rem;
            color: #28a745;
            font-weight: bold;
        }

        .hardware-value {
            min-width: 120px;
            max-width: 150px;
            font-size: 0.8rem;
            color: #333;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="debug-container">
            <h1>排行榜调试页面</h1>
            
            <div class="debug-info">
                <h3>调试信息:</h3>
                <p><strong>排行榜数据长度:</strong> {{ leaderboard.length }}</p>
                <p><strong>最大分数:</strong> {{ maxScore }}</p>
                <p><strong>显示选项:</strong> {{ JSON.stringify(displayOptions) }}</p>
            </div>

            <div v-if="leaderboard.length === 0">
                <p>正在加载排行榜数据...</p>
            </div>

            <div v-for="(item, index) in leaderboard" :key="item.submission_id" class="leaderboard-item">
                <div class="rank-badge">{{ index + 1 }}</div>

                <div class="student-info-leaderboard">
                    <div class="student-details">{{ item.student_no }}</div>
                </div>

                <div class="performance-metrics">
                    <div v-if="displayOptions.showScore" class="metric-item score-bar-container">
                        <span class="metric-label">分数:</span>
                        <div class="score-bar">
                            <div class="score-fill"
                                 :style="{
                                     width: getScoreWidth(item.score, maxScore) + '%'
                                 }">
                            </div>
                        </div>
                        <span class="score-value">{{ item.score.toFixed(1) }}</span>
                    </div>
                    
                    <div v-if="displayOptions.showRuntime" class="metric-item">
                        <span class="metric-label">时间:</span>
                        <span class="metric-value">{{ item.runtime.toFixed(3) }}s</span>
                    </div>
                    
                    <div v-if="displayOptions.showHardware && item.hardware" class="metric-item">
                        <span class="metric-label">硬件:</span>
                        <span class="hardware-value" :title="item.hardware">{{ item.hardware }}</span>
                    </div>
                </div>

                <div class="debug-info" style="margin-left: auto; font-size: 0.8rem;">
                    宽度: {{ getScoreWidth(item.score, maxScore) }}%
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed, onMounted } = Vue;

        createApp({
            setup() {
                const leaderboard = ref([]);
                const displayOptions = ref({
                    showScore: true,
                    showRuntime: true,
                    showHardware: true
                });

                const maxScore = computed(() => {
                    const max = leaderboard.value.length > 0 ?
                        Math.max(...leaderboard.value.map(item => item.score)) : 0;
                    console.log('计算最大分数:', max);
                    return max;
                });

                const getScoreWidth = (score, maxScore) => {
                    if (maxScore === 0) return 0;
                    const width = Math.min((score / maxScore) * 100, 100);
                    console.log(`分数: ${score}, 最大分数: ${maxScore}, 宽度: ${width}%`);
                    return width;
                };

                const loadLeaderboard = async () => {
                    try {
                        const response = await fetch('/display/leaderboard?question_id=1&limit=10');
                        if (response.ok) {
                            const data = await response.json();
                            leaderboard.value = data.leaderboard || [];
                            console.log('加载的排行榜数据:', leaderboard.value);
                        }
                    } catch (error) {
                        console.error('加载排行榜失败:', error);
                    }
                };

                onMounted(() => {
                    loadLeaderboard();
                });

                return {
                    leaderboard,
                    displayOptions,
                    maxScore,
                    getScoreWidth,
                    JSON
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
