* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    color: #667eea;
    font-size: 1.8rem;
    font-weight: 700;
}

.header .user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.main-content {
    flex: 1;
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-card h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #667eea;
    font-size: 2rem;
}

.nav-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 0.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow-x: auto;
}

.nav-tab {
    flex: 1;
    padding: 1rem 1.5rem;
    text-align: center;
    cursor: pointer;
    border-radius: 10px;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-weight: 500;
    min-width: 120px;
}

.nav-tab:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.nav-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.content-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(220, 53, 69, 0.4);
}

.btn-outline {
    background: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover:not(:disabled) {
    background: #667eea;
    color: white;
}

.table-container {
    overflow-x: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid #e1e5e9;
}

.table tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-inactive {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.status-open {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

.status-closed {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    color: #666;
    font-weight: 600;
    margin-top: 0.5rem;
}

.alert {
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.alert-error {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
    opacity: 0;
}

.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
}

.notification-item {
    margin-bottom: 10px;
    cursor: pointer;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
    margin-bottom: 1.5rem;
    color: #667eea;
    text-align: center;
}

.modal-content textarea {
    resize: vertical;
    min-height: 80px;
}

@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }

    .header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .nav-tabs {
        flex-direction: column;
    }

    .nav-tab {
        min-width: auto;
    }

    .modal-content {
        margin: 1rem;
        width: calc(100% - 2rem);
    }
}

/* Vue Progress Bar 样式 */
.vue-progress-bar {
    display: flex;
    align-items: center;
    width: 100%;
}

.progress-track {
    flex: 1;
    position: relative;
    border-radius: 100px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: inherit;
    position: relative;
    overflow: hidden;
    transition: width 0.6s ease;
}

.progress-text {
    white-space: nowrap;
    font-size: 14px;
    color: #606266;
    line-height: 1;
}

/* 条纹动画 */
@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

/* 硬件性能排行榜样式 */
.hardware-leaderboard {
    padding: 2rem;
}

.leaderboard-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.leaderboard-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.leaderboard-item:hover {
    background-color: #f8f9fa;
}

.leaderboard-item.top-performer {
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
}

.rank-badge {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
    flex-shrink: 0;
}

.rank-badge.rank-1 {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #b8860b;
    font-size: 1.2rem;
}

.rank-badge.rank-2 {
    background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
    color: #666;
    font-size: 1.1rem;
}

.rank-badge.rank-3 {
    background: linear-gradient(135deg, #cd7f32, #daa520);
    color: #8b4513;
    font-size: 1.1rem;
}

.rank-badge:not(.rank-1):not(.rank-2):not(.rank-3) {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #dee2e6;
}

.student-info {
    flex: 1;
    margin-right: 1rem;
}

.student-name {
    font-weight: bold;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.student-details {
    color: #666;
    font-size: 0.9rem;
}

.performance-info {
    flex: 2;
    margin-right: 1rem;
}

.score-bar-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.score-label {
    font-weight: bold;
    color: #333;
    min-width: 60px;
    text-align: right;
}

/* 为排行榜中的进度条优化样式 */
.leaderboard-item .vue-progress-bar {
    flex: 1;
    max-width: 200px;
    min-width: 150px;
}

.leaderboard-item .progress-track {
    background: #e9ecef;
    border-radius: 10px;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.leaderboard-item .progress-bar {
    background: linear-gradient(90deg, currentColor, rgba(255,255,255,0.2));
    border-radius: 10px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 保留原有的score-bar样式以兼容其他页面 */
.score-bar {
    flex: 1;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.score-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.3s ease;
    background: linear-gradient(90deg, currentColor, rgba(255,255,255,0.2));
}

.runtime-label {
    color: #666;
    font-size: 0.9rem;
    min-width: 50px;
}

.submission-time {
    color: #666;
    font-size: 0.85rem;
    text-align: right;
    min-width: 120px;
}
