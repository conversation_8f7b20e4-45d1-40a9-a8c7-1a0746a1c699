<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试控制面板</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
        }

        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 15px;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            min-width: 200px;
        }

        .control-panel h3 {
            margin-bottom: 1rem;
            font-size: 1.1rem;
            color: #667eea;
        }

        .control-option {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            cursor: pointer;
        }

        .control-option input {
            margin-right: 0.5rem;
        }

        .view-toggle {
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .main-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
            padding-top: 100px;
        }

        .debug-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            text-align: left;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 控制面板 -->
        <div class="control-panel">
            <h3><i class="fas fa-cog"></i> 显示控制</h3>
            
            <div class="view-toggle">
                <div class="control-option">
                    <input type="radio" id="view-submissions" value="submissions" v-model="viewMode">
                    <label for="view-submissions">提交展示</label>
                </div>
                <div class="control-option">
                    <input type="radio" id="view-leaderboard" value="leaderboard" v-model="viewMode">
                    <label for="view-leaderboard">性能排行榜</label>
                </div>
            </div>

            <div v-if="viewMode === 'leaderboard'">
                <div class="control-option">
                    <input type="checkbox" id="show-score" v-model="displayOptions.showScore">
                    <label for="show-score">显示分数条</label>
                </div>
                <div class="control-option">
                    <input type="checkbox" id="show-runtime" v-model="displayOptions.showRuntime">
                    <label for="show-runtime">显示运行时间</label>
                </div>
                <div class="control-option">
                    <input type="checkbox" id="show-hardware" v-model="displayOptions.showHardware">
                    <label for="show-hardware">显示硬件信息</label>
                </div>
            </div>
        </div>

        <div class="main-content">
            <h1>
                <i :class="viewMode === 'submissions' ? 'fas fa-chalkboard-teacher' : 'fas fa-trophy'"></i>
                {{ viewMode === 'submissions' ? '课堂展示' : '性能排行榜' }}
            </h1>
            
            <div class="debug-info">
                <h3>调试信息：</h3>
                <p><strong>当前视图模式：</strong> {{ viewMode }}</p>
                <p><strong>显示选项：</strong></p>
                <ul>
                    <li>显示分数条: {{ displayOptions.showScore }}</li>
                    <li>显示运行时间: {{ displayOptions.showRuntime }}</li>
                    <li>显示硬件信息: {{ displayOptions.showHardware }}</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref } = Vue;

        createApp({
            setup() {
                const viewMode = ref('submissions');
                const displayOptions = ref({
                    showScore: true,
                    showRuntime: true,
                    showHardware: true
                });

                return {
                    viewMode,
                    displayOptions
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
