<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课堂展示</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background:
                radial-gradient(circle at 20% 80%, #2c3e50 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, #34495e 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, #1a252f 0%, transparent 50%),
                linear-gradient(135deg, #0f1419 0%, #1a252f 50%, #2c3e50 100%);
            color: #ecf0f1;
            overflow-x: hidden;
            min-height: 100vh;
        }

        .display-container {
            min-height: 100vh;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            background: linear-gradient(145deg, #2c3e50, #34495e);
            padding: 2rem;
            border-radius: 16px;
            box-shadow:
                0 8px 32px rgba(0,0,0,0.3),
                inset 0 1px 0 rgba(255,255,255,0.1);
            border: 1px solid #4a5568;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c, #9b59b6);
            animation: rainbow 3s linear infinite;
        }

        @keyframes rainbow {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 20px rgba(52, 152, 219, 0.5);
            background: linear-gradient(45deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            color: #bdc3c7;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .submissions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        .submission-card {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .submission-card:hover {
            transform: translateY(-5px);
        }

        .student-info {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #eee;
        }

        .student-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 1rem;
        }

        .submission-content {
            margin-bottom: 1rem;
        }

        .submission-image {
            width: 100%;
            max-height: 200px;
            object-fit: cover;
            border-radius: 10px;
            margin-bottom: 1rem;
        }

        .submission-text {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            white-space: pre-wrap;
            max-height: 150px;
            overflow-y: auto;
        }

        .submission-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #666;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 50vh;
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 6px solid rgba(255,255,255,0.3);
            border-top: 6px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-submissions {
            text-align: center;
            padding: 4rem;
            color: rgba(255,255,255,0.8);
        }

        .no-submissions i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* 控制面板样式 */
        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 15px;
            padding: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            min-width: 200px;
        }

        .control-panel h3 {
            margin-bottom: 1rem;
            font-size: 1.1rem;
            color: #667eea;
        }

        .control-option {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            cursor: pointer;
        }

        .control-option input[type="checkbox"] {
            margin-right: 0.5rem;
        }

        .control-option input[type="radio"] {
            margin-right: 0.5rem;
        }

        .view-toggle {
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        /* 排行榜样式 */
        .leaderboard-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .leaderboard-item {
            background: linear-gradient(145deg, #2c3e50, #34495e);
            color: #ecf0f1;
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow:
                0 8px 32px rgba(0,0,0,0.3),
                inset 0 1px 0 rgba(255,255,255,0.1);
            border: 1px solid #4a5568;
            display: flex;
            align-items: center;
            gap: 1rem;
            min-height: 80px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .leaderboard-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #3498db, transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .leaderboard-item:hover {
            transform: translateY(-2px);
            box-shadow:
                0 12px 40px rgba(0,0,0,0.4),
                inset 0 1px 0 rgba(255,255,255,0.2);
        }

        .leaderboard-item:hover::before {
            opacity: 1;
        }

        .rank-badge {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
            flex-shrink: 0;
            position: relative;
            box-shadow: 0 4px 16px rgba(0,0,0,0.3);
            border: 2px solid rgba(255,255,255,0.2);
        }

        .rank-1 {
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #b8860b;
        }

        .rank-2 {
            background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
            color: #666;
        }

        .rank-3 {
            background: linear-gradient(135deg, #cd7f32, #daa520);
            color: #8b4513;
        }

        .rank-other {
            background: linear-gradient(135deg, #6c757d, #495057);
            color: #fff;
            border: 2px solid rgba(255,255,255,0.2);
        }

        .performance-metrics {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .student-info-section {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .student-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: #ecf0f1;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .hardware-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #bdc3c7;
            opacity: 0.8;
        }

        .hardware-info i {
            color: #3498db;
        }

        .performance-bars {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .runtime-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(145deg, #34495e, #2c3e50);
            padding: 0.8rem 1rem;
            border-radius: 8px;
            border: 1px solid #4a5568;
        }

        .runtime-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #bdc3c7;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .runtime-label i {
            color: #f39c12;
        }

        .runtime-value {
            color: #ecf0f1;
            font-size: 1.1rem;
            font-weight: 600;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 8px rgba(255,255,255,0.3);
        }

        .student-info-leaderboard {
            min-width: 120px;
            flex-shrink: 0;
        }

        .student-details {
            color: #666;
            font-size: 1rem;
            font-weight: 500;
        }

        .performance-metrics {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex: 1;
        }

        .metric-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .score-bar-container {
            flex: 1;
            max-width: 500px;
            min-width: 200px;
        }

        .score-bar {
            height: 25px;
            background: #e9ecef !important;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
            border: 1px solid #ccc; /* 添加边框以便调试 */
        }

        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997) !important;
            border-radius: 12px;
            transition: width 0.8s ease;
            position: relative;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
            min-width: 2px; /* 确保即使很小的值也能显示 */
        }

        .score-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            background: linear-gradient(180deg, rgba(255,255,255,0.3), transparent);
            border-radius: 12px 12px 0 0;
        }

        .metric-label {
            font-weight: bold;
            min-width: 70px;
            font-size: 0.9rem;
            color: #555;
        }

        .metric-value {
            min-width: 60px;
            font-size: 0.9rem;
            color: #333;
            font-weight: 600;
        }

        .score-value {
            min-width: 70px;
            font-size: 1.1rem;
            color: #28a745;
            font-weight: bold;
        }

        .hardware-value {
            min-width: 120px;
            max-width: 150px;
            font-size: 0.8rem;
            color: #333;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 控制面板 -->
        <div class="control-panel">
            <h3><i class="fas fa-cog"></i> 显示控制</h3>

            <div class="view-toggle">
                <div class="control-option">
                    <input type="radio" id="view-submissions" value="submissions" v-model="viewMode" @change="switchViewMode('submissions')">
                    <label for="view-submissions">提交展示</label>
                </div>
                <div class="control-option">
                    <input type="radio" id="view-leaderboard" value="leaderboard" v-model="viewMode" @change="switchViewMode('leaderboard')">
                    <label for="view-leaderboard">性能排行榜</label>
                </div>
            </div>

            <div v-if="viewMode === 'leaderboard'">
                <div class="control-option">
                    <input type="checkbox" id="show-score" v-model="displayOptions.showScore">
                    <label for="show-score">显示分数条</label>
                </div>
                <div class="control-option">
                    <input type="checkbox" id="show-runtime" v-model="displayOptions.showRuntime">
                    <label for="show-runtime">显示运行时间</label>
                </div>
                <div class="control-option">
                    <input type="checkbox" id="show-hardware" v-model="displayOptions.showHardware">
                    <label for="show-hardware">显示硬件信息</label>
                </div>
            </div>
        </div>

        <div class="display-container">
            <div class="header">
                <h1>
                    <i :class="viewMode === 'submissions' ? 'fas fa-chalkboard-teacher' : 'fas fa-trophy'"></i>
                    {{ viewMode === 'submissions' ? '课堂展示' : '性能排行榜' }}
                </h1>
                <p style="font-size: 1.2rem; opacity: 0.9;">
                    {{ className }} - {{ questionTitle }}
                </p>
            </div>

            <div v-if="loading" class="loading">
                <div class="spinner"></div>
            </div>

            <!-- 提交展示视图 -->
            <div v-else-if="viewMode === 'submissions'">
                <div v-if="submissions.length === 0" class="no-submissions">
                    <i class="fas fa-inbox"></i>
                    <h2>暂无提交</h2>
                    <p>等待学生提交作业...</p>
                </div>

                <div v-else class="submissions-grid">
                    <div v-for="submission in submissions" :key="submission.id" class="submission-card">
                        <div class="student-info">
                            <div class="student-avatar">
                                {{ getStudentInitial(submission.student_name) }}
                            </div>
                            <div>
                                <div style="font-weight: bold;">{{ submission.student_name }}</div>
                                <div style="color: #666; font-size: 0.9rem;">{{ submission.student_no }}</div>
                            </div>
                        </div>

                        <div class="submission-content">
                            <div v-if="submission.image_url">
                                <img :src="submission.image_url" class="submission-image" alt="提交图片">
                            </div>
                            <div v-if="submission.text_data" class="submission-text">
                                {{ submission.text_data }}
                            </div>
                        </div>

                        <div class="submission-meta">
                            <span>
                                <i class="fas fa-clock"></i>
                                {{ formatDateTime(submission.created_at) }}
                            </span>
                            <span v-if="submission.score">
                                <i class="fas fa-star"></i>
                                {{ submission.score }}分
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 排行榜视图 -->
            <div v-else-if="viewMode === 'leaderboard'" class="leaderboard-container">
                <div v-if="leaderboard.length === 0" class="no-submissions">
                    <i class="fas fa-trophy"></i>
                    <h2>暂无排行数据</h2>
                    <p>等待学生提交带有运行时间的作业...</p>
                </div>

                <div v-else>
                    <div v-for="(item, index) in leaderboard" :key="item.submission_id" class="leaderboard-item">
                        <div class="rank-badge" :class="{
                            'rank-1': index === 0,
                            'rank-2': index === 1,
                            'rank-3': index === 2,
                            'rank-other': index > 2
                        }">
                            <i v-if="index === 0" class="fas fa-crown"></i>
                            <i v-else-if="index === 1" class="fas fa-medal"></i>
                            <i v-else-if="index === 2" class="fas fa-award"></i>
                            <span v-else>{{ index + 1 }}</span>
                        </div>

                        <div class="performance-metrics">
                            <div class="student-info-section">
                                <div class="student-name">{{ item.student_no }}</div>
                                <div class="hardware-info" v-if="displayOptions.showHardware && item.hardware">
                                    <i class="fas fa-microchip"></i>
                                    {{ item.hardware }}
                                </div>
                            </div>

                            <div class="performance-bars">
                                <hardware-progress-bar
                                    v-if="displayOptions.showScore"
                                    :percentage="getScoreBarWidth(item.score)"
                                    :score="item.score"
                                    :max-score="maxScore"
                                    label="性能得分"
                                    unit="分"
                                    :show-rank="true"
                                    :rank="index + 1"
                                    :animated="true"
                                    :glow-effect="true"
                                ></hardware-progress-bar>

                                <div v-if="displayOptions.showRuntime" class="runtime-display">
                                    <div class="runtime-label">
                                        <i class="fas fa-stopwatch"></i>
                                        执行时间
                                    </div>
                                    <div class="runtime-value">
                                        {{ item.runtime.toFixed(3) }}s
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/components/ProgressBar.js"></script>
    <script src="/static/js/components/HardwareProgressBar.js"></script>
    <script>
        const { createApp, ref, onMounted, computed } = Vue;

        // 确保组件已加载
        if (typeof ProgressBar === 'undefined' && typeof window.ProgressBar === 'undefined') {
            console.error('ProgressBar 组件未加载！请检查 ProgressBar.js 文件是否正确加载。');
        }
        if (typeof HardwareProgressBar === 'undefined' && typeof window.HardwareProgressBar === 'undefined') {
            console.error('HardwareProgressBar 组件未加载！请检查 HardwareProgressBar.js 文件是否正确加载。');
        }

        const app = createApp({
            components: {
                'progress-bar': window.ProgressBar || ProgressBar,
                'hardware-progress-bar': window.HardwareProgressBar || HardwareProgressBar
            },
            setup() {
                const submissions = ref([]);
                const leaderboard = ref([]);
                const loading = ref(true);
                const questionTitle = ref('');
                const className = ref('');
                const viewMode = ref('submissions'); // 'submissions' 或 'leaderboard'
                const displayOptions = ref({
                    showScore: true,
                    showRuntime: true,
                    showHardware: true
                });

                const getUrlParams = () => {
                    const params = new URLSearchParams(window.location.search);
                    const classIdParam = params.get('class_id');
                    // 支持多个班级ID，用逗号分隔
                    const classIds = classIdParam ? classIdParam.split(',').map(id => parseInt(id.trim())) : [];
                    return {
                        class_ids: classIds,
                        question_id: params.get('question_id')
                    };
                };

                const loadSubmissions = async () => {
                    const { class_ids, question_id } = getUrlParams();

                    if (!class_ids.length || !question_id) {
                        console.error('缺少必要参数');
                        loading.value = false;
                        return;
                    }

                    try {
                        // 构建多个class_id参数的URL
                        const params = new URLSearchParams();
                        class_ids.forEach(id => params.append('class_id', id));
                        params.append('question_id', question_id);

                        const response = await fetch(`/display/submissions?${params.toString()}`);
                        if (response.ok) {
                            const data = await response.json();
                            submissions.value = data.submissions || [];
                            questionTitle.value = data.question_title || '未知题目';
                            className.value = data.class_name || '未知班级';
                        }
                    } catch (error) {
                        console.error('加载提交数据失败:', error);
                    } finally {
                        loading.value = false;
                    }
                };

                const loadLeaderboard = async () => {
                    const { class_ids, question_id } = getUrlParams();

                    if (!question_id) {
                        console.error('缺少题目ID参数');
                        return;
                    }

                    try {
                        const params = new URLSearchParams();
                        if (class_ids.length > 0) {
                            params.append('class_id', class_ids[0]); // 使用第一个班级ID
                        }
                        params.append('question_id', question_id);
                        params.append('limit', '50');

                        const response = await fetch(`/display/leaderboard?${params.toString()}`);
                        if (response.ok) {
                            const data = await response.json();
                            leaderboard.value = data.leaderboard || [];
                            if (data.question_title) {
                                questionTitle.value = data.question_title;
                            }
                        }
                    } catch (error) {
                        console.error('加载排行榜失败:', error);
                    }
                };

                const formatDateTime = (dateString) => {
                    if (!dateString) return '';
                    return new Date(dateString).toLocaleString('zh-CN');
                };

                const getStudentInitial = (name) => {
                    return name ? name.charAt(0).toUpperCase() : '?';
                };

                const getScoreColor = (score) => {
                    if (score >= 100) return '#28a745';
                    if (score >= 50) return '#ffc107';
                    if (score >= 20) return '#fd7e14';
                    return '#dc3545';
                };

                const getScoreWidth = (score, maxScore) => {
                    if (maxScore === 0) return 0;
                    return Math.min((score / maxScore) * 100, 100);
                };

                const getScoreBarWidth = (score) => {
                    const currentMaxScore = leaderboard.value.length > 0 ?
                        Math.max(...leaderboard.value.map(item => item.score)) : 0;
                    if (currentMaxScore === 0) return 0;
                    const width = Math.min((score / currentMaxScore) * 100, 100);
                    return Math.max(width, 1); // 确保至少有1%的宽度
                };

                const maxScore = computed(() => {
                    return leaderboard.value.length > 0 ?
                        Math.max(...leaderboard.value.map(item => item.score)) : 0;
                });

                const switchViewMode = (mode) => {
                    viewMode.value = mode;
                    if (mode === 'leaderboard') {
                        loadLeaderboard();
                    }
                };

                const loadData = () => {
                    if (viewMode.value === 'submissions') {
                        loadSubmissions();
                    } else {
                        loadLeaderboard();
                    }
                };

                // 定期刷新数据
                const startAutoRefresh = () => {
                    setInterval(loadData, 10000); // 每10秒刷新一次
                };

                onMounted(() => {
                    loadSubmissions();
                    startAutoRefresh();
                });

                return {
                    submissions,
                    leaderboard,
                    loading,
                    questionTitle,
                    className,
                    viewMode,
                    displayOptions,
                    maxScore,
                    formatDateTime,
                    getStudentInitial,
                    getScoreColor,
                    getScoreWidth,
                    getScoreBarWidth,
                    switchViewMode
                };
            }
        });

        // 添加错误处理
        app.config.errorHandler = (err, vm, info) => {
            console.error('Vue 应用错误:', err);
            console.error('错误信息:', info);
            console.error('组件实例:', vm);
        };

        // 挂载应用
        app.mount('#app');
    </script>
</body>
</html>