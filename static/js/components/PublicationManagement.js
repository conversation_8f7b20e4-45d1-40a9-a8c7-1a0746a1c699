// 发布管理组件
const PublicationManagement = {
    setup() {
        const { ref, onMounted } = Vue;
        const { get, patch, del } = useApi();
        const { success, error } = useNotification();
        
        const publications = ref([]);
        const classes = ref([]);
        const selectedClass = ref('');
        const loading = ref(true);

        const loadClasses = async () => {
            try {
                classes.value = await get('/admin/classes');
            } catch (err) {
                console.error('加载班级列表失败:', err);
            }
        };

        const loadPublications = async () => {
            try {
                const params = selectedClass.value ? { class_id: selectedClass.value } : {};
                publications.value = await get('/admin/question-publications', params);
            } catch (err) {
                error('加载发布管理失败');
            } finally {
                loading.value = false;
            }
        };

        const handleClassChange = () => {
            loading.value = true;
            loadPublications();
        };

        const togglePublication = async (publication) => {
            try {
                await patch(`/admin/question-publications/${publication.id}/toggle`);
                success(`题目已${publication.is_active ? '关闭' : '开放'}`);
                await loadPublications();
            } catch (err) {
                error('状态更新失败');
            }
        };

        const handleDelete = async (publication) => {
            if (!confirm(`确定要删除发布"${publication.question_title}"到"${publication.class_name}"吗？`)) {
                return;
            }

            try {
                await del(`/admin/question-publications/${publication.id}`);
                success('发布删除成功');
                await loadPublications();
            } catch (err) {
                error('发布删除失败');
            }
        };

        onMounted(async () => {
            await loadClasses();
            await loadPublications();
        });

        return {
            publications,
            classes,
            selectedClass,
            loading,
            handleClassChange,
            togglePublication,
            handleDelete,
            formatDateTime: helpers.formatDateTime
        };
    },
    template: `
        <div class="content-card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h2>
                    <i class="fas fa-broadcast-tower"></i> 
                    发布管理
                </h2>
            </div>
            
            <div style="background: rgba(102, 126, 234, 0.1); padding: 1rem; border-radius: 10px; margin-bottom: 2rem;">
                <p style="margin: 0; color: #667eea;">
                    <i class="fas fa-info-circle"></i> 
                    发布管理展示已发布到具体班级的题目实例，可进行启用/停用与删除操作
                </p>
            </div>
            
            <div class="form-group" style="max-width: 300px; margin-bottom: 2rem;">
                <label>选择班级</label>
                <select v-model="selectedClass" @change="handleClassChange" class="form-control">
                    <option value="">所有班级</option>
                    <option v-for="cls in classes" :key="cls.id" :value="cls.id">
                        {{ cls.name }}
                    </option>
                </select>
            </div>
            
            <div v-if="loading" class="loading">
                <div class="spinner"></div>
            </div>
            
            <div v-else class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>发布ID</th>
                            <th>题目名称</th>
                            <th>班级</th>
                            <th>发布状态</th>
                            <th>提交数</th>
                            <th>发布时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="pub in publications" :key="pub.id">
                            <td><strong>{{ pub.id }}</strong></td>
                            <td>{{ pub.question_title }} <small class="text-muted">(题目ID: {{ pub.question_id }})</small></td>
                            <td>{{ pub.class_name }}</td>
                            <td>
                                <span :class="['status-badge', pub.is_active ? 'status-open' : 'status-closed']">
                                    {{ pub.is_active ? '开放' : '关闭' }}
                                </span>
                            </td>
                            <td>{{ pub.submission_count || 0 }}</td>
                            <td>{{ formatDateTime(pub.created_at) }}</td>
                            <td>
                                <button class="btn btn-outline" @click="togglePublication(pub)">
                                    <i :class="pub.is_active ? 'fas fa-pause' : 'fas fa-play'"></i>
                                    {{ pub.is_active ? '关闭' : '开放' }}
                                </button>
                                <button class="btn btn-danger" @click="handleDelete(pub)">
                                    <i class="fas fa-trash"></i>
                                    删除
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <div v-if="publications.length === 0" style="text-align: center; padding: 2rem; color: #666;">
                    <i class="fas fa-broadcast-tower" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                    <p>{{ selectedClass ? '该班级暂无发布的题目' : '暂无发布的题目' }}</p>
                </div>
            </div>
        </div>
    `
};

// 全局注册
window.PublicationManagement = PublicationManagement;
