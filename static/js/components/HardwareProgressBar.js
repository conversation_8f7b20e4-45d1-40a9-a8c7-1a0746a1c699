// 硬件风格进度条组件 - CPU测评风格
const HardwareProgressBar = {
    props: {
        percentage: {
            type: Number,
            default: 0,
            validator: (value) => value >= 0 && value <= 100
        },
        score: {
            type: Number,
            default: 0
        },
        maxScore: {
            type: Number,
            default: 100
        },
        label: {
            type: String,
            default: '性能'
        },
        unit: {
            type: String,
            default: '分'
        },
        showRank: {
            type: Boolean,
            default: false
        },
        rank: {
            type: Number,
            default: 0
        },
        animated: {
            type: Boolean,
            default: true
        },
        glowEffect: {
            type: Boolean,
            default: true
        }
    },
    setup(props) {
        const { computed, ref, onMounted, watch } = Vue;
        
        const displayScore = ref(0);
        const displayPercentage = ref(0);
        
        // 性能等级计算
        const performanceLevel = computed(() => {
            if (props.percentage >= 90) return 'legendary';
            if (props.percentage >= 75) return 'excellent';
            if (props.percentage >= 60) return 'good';
            if (props.percentage >= 40) return 'average';
            return 'poor';
        });
        
        // 颜色计算
        const getPerformanceColor = computed(() => {
            const level = performanceLevel.value;
            const colors = {
                legendary: { primary: '#ff6b35', secondary: '#f7931e' }, // 橙红渐变
                excellent: { primary: '#00d4aa', secondary: '#00a8cc' }, // 青色渐变
                good: { primary: '#4ecdc4', secondary: '#44a08d' },      // 绿色渐变
                average: { primary: '#ffd93d', secondary: '#ff9a56' },   // 黄色渐变
                poor: { primary: '#ff6b6b', secondary: '#ee5a52' }       // 红色渐变
            };
            return colors[level];
        });
        
        // 进度条样式
        const progressBarStyle = computed(() => {
            const colors = getPerformanceColor.value;
            const glowColor = colors.primary;
            
            return {
                width: `${displayPercentage.value}%`,
                background: `linear-gradient(90deg, ${colors.primary} 0%, ${colors.secondary} 100%)`,
                boxShadow: props.glowEffect ? 
                    `0 0 20px ${glowColor}40, 0 0 40px ${glowColor}20, inset 0 1px 0 rgba(255,255,255,0.3)` : 
                    'none',
                transition: props.animated ? 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)' : 'none'
            };
        });
        
        // 轨道样式
        const trackStyle = computed(() => ({
            background: 'linear-gradient(90deg, #2c3e50 0%, #34495e 100%)',
            boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.3), 0 1px 0 rgba(255,255,255,0.1)',
            border: '1px solid #34495e'
        }));
        
        // 数值动画
        const animateValue = (target, current, setter) => {
            if (!props.animated) {
                setter(target);
                return;
            }
            
            const duration = 1000;
            const startTime = Date.now();
            const startValue = current;
            const difference = target - startValue;
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const easeProgress = 1 - Math.pow(1 - progress, 3); // easeOutCubic
                
                const currentValue = startValue + (difference * easeProgress);
                setter(currentValue);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            };
            
            requestAnimationFrame(animate);
        };
        
        // 监听数值变化
        watch(() => props.score, (newScore) => {
            animateValue(newScore, displayScore.value, (val) => displayScore.value = val);
        });
        
        watch(() => props.percentage, (newPercentage) => {
            animateValue(newPercentage, displayPercentage.value, (val) => displayPercentage.value = val);
        });
        
        onMounted(() => {
            displayScore.value = props.score;
            displayPercentage.value = props.percentage;
        });
        
        return {
            displayScore,
            displayPercentage,
            performanceLevel,
            progressBarStyle,
            trackStyle,
            getPerformanceColor
        };
    },
    template: `
        <div class="hardware-progress-container">
            <div class="hardware-progress-header">
                <div class="performance-label">
                    <i class="fas fa-microchip"></i>
                    {{ label }}
                </div>
                <div class="performance-value">
                    <span class="score-number">{{ Math.round(displayScore) }}</span>
                    <span class="score-unit">{{ unit }}</span>
                    <div v-if="showRank" class="rank-badge" :class="'rank-' + performanceLevel">
                        #{{ rank }}
                    </div>
                </div>
            </div>
            
            <div class="hardware-progress-track" :style="trackStyle">
                <div class="hardware-progress-bar" :style="progressBarStyle">
                    <div class="progress-shine"></div>
                    <div class="progress-segments">
                        <div v-for="i in 10" :key="i" class="segment"></div>
                    </div>
                </div>
                <div class="progress-markers">
                    <div v-for="i in 5" :key="i" class="marker" :style="{ left: (i * 20) + '%' }"></div>
                </div>
            </div>
            
            <div class="hardware-progress-footer">
                <div class="performance-level" :class="performanceLevel">
                    <i :class="getLevelIcon()"></i>
                    {{ getLevelText() }}
                </div>
                <div class="percentage-display">
                    {{ Math.round(displayPercentage) }}%
                </div>
            </div>
        </div>
    `,
    methods: {
        getLevelIcon() {
            const icons = {
                legendary: 'fas fa-crown',
                excellent: 'fas fa-star',
                good: 'fas fa-thumbs-up',
                average: 'fas fa-minus-circle',
                poor: 'fas fa-exclamation-triangle'
            };
            return icons[this.performanceLevel] || 'fas fa-question';
        },
        getLevelText() {
            const texts = {
                legendary: '传奇',
                excellent: '优秀',
                good: '良好',
                average: '一般',
                poor: '较差'
            };
            return texts[this.performanceLevel] || '未知';
        }
    }
};

// 全局注册
window.HardwareProgressBar = HardwareProgressBar;
