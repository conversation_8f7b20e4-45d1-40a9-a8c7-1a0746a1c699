// Vue3 Progress Bar Component - 类似 vue3-progress-bar 的功能
const ProgressBar = {
    props: {
        percentage: {
            type: Number,
            default: 0,
            validator: (value) => value >= 0 && value <= 100
        },
        color: {
            type: String,
            default: '#409eff'
        },
        strokeWidth: {
            type: Number,
            default: 6
        },
        textInside: {
            type: Boolean,
            default: false
        },
        status: {
            type: String,
            default: '',
            validator: (value) => ['', 'success', 'exception', 'warning'].includes(value)
        },
        showText: {
            type: Boolean,
            default: true
        },
        format: {
            type: Function,
            default: (percentage) => `${percentage}%`
        },
        striped: {
            type: Boolean,
            default: false
        },
        animated: {
            type: Boolean,
            default: false
        }
    },
    setup(props) {
        const { computed } = Vue;

        const getBarColor = () => {
            if (props.status === 'success') return '#67c23a';
            if (props.status === 'exception') return '#f56c6c';
            if (props.status === 'warning') return '#e6a23c';
            return props.color;
        };

        const barStyle = computed(() => {
            const style = {
                width: `${props.percentage}%`,
                backgroundColor: getBarColor(),
                height: `${props.strokeWidth}px`,
                transition: 'width 0.6s ease',
                borderRadius: `${props.strokeWidth / 2}px`
            };

            if (props.striped) {
                style.backgroundImage = 'linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent)';
                style.backgroundSize = '1rem 1rem';
            }

            if (props.animated && props.striped) {
                style.animation = 'progress-bar-stripes 1s linear infinite';
            }

            return style;
        });
        
        const trackStyle = computed(() => ({
            height: `${props.strokeWidth}px`,
            backgroundColor: '#f5f5f5',
            borderRadius: `${props.strokeWidth / 2}px`,
            overflow: 'hidden',
            position: 'relative'
        }));

        const getStatusIcon = () => {
            if (props.status === 'success') return 'fas fa-check';
            if (props.status === 'exception') return 'fas fa-times';
            if (props.status === 'warning') return 'fas fa-exclamation';
            return '';
        };
        
        const textStyle = computed(() => {
            if (props.textInside) {
                return {
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    color: '#fff',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    textShadow: '0 0 2px rgba(0,0,0,0.5)'
                };
            }
            return {
                marginLeft: '10px',
                fontSize: '14px',
                color: '#606266'
            };
        });

        return {
            barStyle,
            trackStyle,
            textStyle,
            getStatusIcon
        };
    },
    template: `
        <div class="vue-progress-bar">
            <div class="progress-track" :style="trackStyle">
                <div class="progress-bar" :style="barStyle">
                    <div v-if="textInside && showText" class="progress-text" :style="textStyle">
                        {{ format(percentage) }}
                    </div>
                </div>
            </div>
            <div v-if="!textInside && showText" class="progress-text" :style="textStyle">
                <i v-if="getStatusIcon()" :class="getStatusIcon()"></i>
                {{ format(percentage) }}
            </div>
        </div>
    `
};

// 全局注册
window.ProgressBar = ProgressBar;
