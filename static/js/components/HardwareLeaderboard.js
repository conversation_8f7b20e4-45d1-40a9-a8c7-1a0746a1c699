// 硬件性能排行榜组件
const HardwareLeaderboard = {
    components: {
        'progress-bar': ProgressBar
    },
    setup() {
        const { get } = useApi();
        const { error } = useNotification();
        
        const leaderboard = ref([]);
        const questions = ref([]);
        const classes = ref([]);
        const selectedQuestion = ref('');
        const selectedClass = ref('');
        const loading = ref(true);
        const totalCount = ref(0);

        const loadQuestions = async () => {
            try {
                const data = await get('/admin/questions');
                questions.value = data;
            } catch (err) {
                console.error('加载题目失败:', err);
            }
        };

        const loadClasses = async () => {
            try {
                const data = await get('/admin/classes');
                classes.value = data;
            } catch (err) {
                console.error('加载班级失败:', err);
            }
        };

        const loadLeaderboard = async () => {
            loading.value = true;
            try {
                const params = {};
                if (selectedQuestion.value) params.question_id = selectedQuestion.value;
                if (selectedClass.value) params.class_id = selectedClass.value;
                
                const data = await get('/admin/hardware-leaderboard', params);
                leaderboard.value = data.leaderboard;
                totalCount.value = data.total_count;
            } catch (err) {
                error('加载排行榜失败');
                console.error('加载排行榜失败:', err);
            } finally {
                loading.value = false;
            }
        };

        const handleFilterChange = () => {
            loadLeaderboard();
        };

        const getScoreColor = (score) => {
            if (score >= 100) return '#28a745'; // 绿色
            if (score >= 50) return '#ffc107';  // 黄色
            if (score >= 20) return '#fd7e14';  // 橙色
            return '#dc3545'; // 红色
        };

        const getScoreWidth = (score, maxScore) => {
            if (maxScore === 0) return 0;
            return Math.min((score / maxScore) * 100, 100);
        };

        const maxScore = computed(() => {
            return leaderboard.value.length > 0 ? Math.max(...leaderboard.value.map(item => item.score)) : 0;
        });

        onMounted(async () => {
            await Promise.all([
                loadQuestions(),
                loadClasses(),
                loadLeaderboard()
            ]);
        });

        return {
            leaderboard,
            questions,
            classes,
            selectedQuestion,
            selectedClass,
            loading,
            totalCount,
            maxScore,
            handleFilterChange,
            getScoreColor,
            getScoreWidth,
            formatDateTime: helpers.formatDateTime
        };
    },
    template: `
        <div class="hardware-leaderboard">
            <div class="page-header">
                <h2>
                    <i class="fas fa-trophy"></i>
                    硬件性能排行榜
                </h2>
                <p class="page-description">基于运行时间的硬件性能评测排行榜（分数 = 1000 / 运行时间）</p>
            </div>
            
            <!-- 筛选器 -->
            <div class="filters" style="display: flex; gap: 1rem; margin-bottom: 2rem; align-items: center;">
                <div class="form-group" style="margin: 0;">
                    <label>题目筛选</label>
                    <select v-model="selectedQuestion" @change="handleFilterChange" class="form-control" style="width: 200px;">
                        <option value="">所有题目</option>
                        <option v-for="question in questions" :key="question.id" :value="question.id">
                            {{ question.title }}
                        </option>
                    </select>
                </div>
                
                <div class="form-group" style="margin: 0;">
                    <label>班级筛选</label>
                    <select v-model="selectedClass" @change="handleFilterChange" class="form-control" style="width: 200px;">
                        <option value="">所有班级</option>
                        <option v-for="cls in classes" :key="cls.id" :value="cls.id">
                            {{ cls.name }}
                        </option>
                    </select>
                </div>
                
                <div style="margin-left: auto; color: #666;">
                    共 {{ totalCount }} 条记录
                </div>
            </div>
            
            <div v-if="loading" class="loading">
                <div class="spinner"></div>
            </div>
            
            <div v-else class="leaderboard-container">
                <div v-if="leaderboard.length === 0" style="text-align: center; padding: 3rem; color: #666;">
                    <i class="fas fa-trophy" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                    <p>暂无性能数据</p>
                    <small>需要提交包含运行时间和硬件信息的作业才会显示在排行榜中</small>
                </div>
                
                <div v-else class="leaderboard-list">
                    <div 
                        v-for="(item, index) in leaderboard" 
                        :key="index"
                        class="leaderboard-item"
                        :class="{ 'top-performer': item.rank <= 3 }"
                    >
                        <div class="rank-badge" :class="'rank-' + Math.min(item.rank, 3)">
                            <i v-if="item.rank === 1" class="fas fa-crown"></i>
                            <i v-else-if="item.rank === 2" class="fas fa-medal"></i>
                            <i v-else-if="item.rank === 3" class="fas fa-award"></i>
                            <span v-else>{{ item.rank }}</span>
                        </div>
                        
                        <div class="student-info">
                            <div class="student-name">{{ item.student_name || item.student_no }}</div>
                            <div class="student-details">
                                学号: {{ item.student_no }} | 硬件: {{ item.hardware }}
                            </div>
                        </div>
                        
                        <div class="performance-info">
                            <div class="score-bar-container">
                                <div class="score-label">{{ item.score }} 分</div>
                                <progress-bar
                                    :percentage="getScoreWidth(item.score, maxScore)"
                                    :color="getScoreColor(item.score)"
                                    :stroke-width="20"
                                    :show-text="false"
                                    :animated="true"
                                    :striped="item.rank <= 3"
                                    :status="item.rank === 1 ? 'success' : ''"
                                ></progress-bar>
                                <div class="runtime-label">{{ item.runtime }}s</div>
                            </div>
                        </div>
                        
                        <div class="submission-time">
                            {{ formatDateTime(item.submitted_at) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `
};

// 全局注册
window.HardwareLeaderboard = HardwareLeaderboard;
