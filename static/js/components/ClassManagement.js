// 班级管理组件
const ClassManagement = {
    setup() {
        const { ref, onMounted } = Vue;
        const { get, post, patch, del } = useApi();
        const { success, error } = useNotification();
        
        const classes = ref([]);
        const loading = ref(true);
        const showModal = ref(false);
        const editingClass = ref(null);
        const form = ref({
            name: '',
            description: ''
        });

        const loadClasses = async () => {
            try {
                classes.value = await get('/admin/classes');
            } catch (err) {
                error('加载班级列表失败');
            } finally {
                loading.value = false;
            }
        };

        const openCreateModal = () => {
            editingClass.value = null;
            form.value = { name: '', description: '' };
            showModal.value = true;
        };

        const openEditModal = (cls) => {
            editingClass.value = cls;
            form.value = { ...cls };
            showModal.value = true;
        };

        const closeModal = () => {
            showModal.value = false;
            editingClass.value = null;
            form.value = { name: '', description: '' };
        };

        const handleSubmit = async () => {
            if (!form.value.name.trim()) {
                error('请输入班级名称');
                return;
            }

            try {
                if (editingClass.value) {
                    // 编辑班级 - 使用 PATCH 方法符合 RESTful 规范
                    await patch(`/admin/classes/${editingClass.value.id}`, form.value);
                    success('班级更新成功');
                } else {
                    // 创建班级
                    await post('/admin/classes', form.value);
                    success('班级创建成功');
                }
                
                closeModal();
                await loadClasses();
            } catch (err) {
                error(editingClass.value ? '班级更新失败' : '班级创建失败');
            }
        };

        const handleDelete = async (cls) => {
            if (!confirm(`确定要删除班级"${cls.name}"吗？`)) {
                return;
            }

            try {
                await del(`/admin/classes/${cls.id}`);
                success('班级删除成功');
                await loadClasses();
            } catch (err) {
                error('班级删除失败');
            }
        };

        onMounted(loadClasses);

        return { 
            classes, 
            loading, 
            showModal,
            form,
            editingClass,
            openCreateModal,
            openEditModal,
            closeModal,
            handleSubmit,
            handleDelete,
            formatDateTime: helpers.formatDateTime
        };
    },
    template: `
        <div class="content-card">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                <h2>
                    <i class="fas fa-school"></i> 
                    班级管理
                </h2>
                <button class="btn btn-primary" @click="openCreateModal">
                    <i class="fas fa-plus"></i> 
                    创建班级
                </button>
            </div>
            
            <div v-if="loading" class="loading">
                <div class="spinner"></div>
            </div>
            
            <div v-else class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>班级名称</th>
                            <th>描述</th>
                            <th>学生数量</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="cls in classes" :key="cls.id">
                            <td>{{ cls.id }}</td>
                            <td>{{ cls.name }}</td>
                            <td>{{ cls.description || '-' }}</td>
                            <td>{{ cls.student_count || 0 }}</td>
                            <td>{{ formatDateTime(cls.created_at) }}</td>
                            <td>
                                <button class="btn btn-outline" @click="openEditModal(cls)">
                                    <i class="fas fa-edit"></i> 
                                    编辑
                                </button>
                                <button class="btn btn-danger" @click="handleDelete(cls)">
                                    <i class="fas fa-trash"></i> 
                                    删除
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 模态框 -->
            <div v-if="showModal" class="modal-overlay" @click="closeModal">
                <div class="modal-content" @click.stop>
                    <h3>{{ editingClass ? '编辑班级' : '创建班级' }}</h3>
                    <form @submit.prevent="handleSubmit">
                        <div class="form-group">
                            <label>班级名称 *</label>
                            <input 
                                type="text" 
                                v-model="form.name" 
                                class="form-control" 
                                required
                                placeholder="请输入班级名称"
                            >
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <textarea 
                                v-model="form.description" 
                                class="form-control" 
                                rows="3"
                                placeholder="请输入班级描述（可选）"
                            ></textarea>
                        </div>
                        <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                            <button type="button" class="btn btn-outline" @click="closeModal">
                                取消
                            </button>
                            <button type="submit" class="btn btn-primary">
                                {{ editingClass ? '更新' : '创建' }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `
};

// 全局注册
window.ClassManagement = ClassManagement;
